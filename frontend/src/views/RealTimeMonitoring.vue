<template>
  <div class="real-time-monitoring">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon class="title-icon"><Monitor /></el-icon>
          实时监控
        </h1>
        <p class="page-description">实时监控系统状态和爬虫任务执行情况</p>
      </div>
      <div class="header-right">
        <el-button @click="refreshAll" size="large">
          <el-icon><Refresh /></el-icon>
          刷新全部
        </el-button>
        <el-button @click="toggleAutoRefresh" :type="autoRefresh ? 'primary' : 'default'" size="large">
          <el-icon><Timer /></el-icon>
          {{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}
        </el-button>
      </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="system-overview">
      <SystemStatus />
    </div>

    <!-- 监控面板 -->
    <div class="monitoring-panels">
      <!-- 左侧：活跃任务监控 -->
      <div class="panel-left">
        <div class="panel-card">
          <div class="panel-header">
            <h3>活跃任务监控</h3>
            <div class="panel-actions">
              <el-tag :type="activeTasks.length > 0 ? 'success' : 'info'" size="small">
                {{ activeTasks.length }} 个活跃任务
              </el-tag>
            </div>
          </div>
          
          <div class="active-tasks-list">
            <div v-if="activeTasks.length === 0" class="empty-state">
              <el-icon class="empty-icon"><Clock /></el-icon>
              <p>当前没有活跃的任务</p>
            </div>
            
            <div v-for="task in activeTasks" :key="task.task_id" class="active-task-item">
              <div class="task-header">
                <div class="task-info">
                  <div class="task-name">{{ task.task_name }}</div>
                  <div class="task-type">
                    <el-tag :type="task.data_type === 'university' ? 'primary' : 'success'" size="small">
                      {{ task.data_type === 'university' ? '大学数据' : '专业数据' }}
                    </el-tag>
                  </div>
                </div>
                <div class="task-status">
                  <el-tag :type="getStatusType(task.status)" size="small">
                    {{ getStatusText(task.status) }}
                  </el-tag>
                </div>
              </div>
              
              <div class="task-progress">
                <div class="progress-info">
                  <span>进度: {{ getTaskProgress(task) }}%</span>
                  <span>运行时间: {{ getRunningTime(task) }}</span>
                </div>
                <el-progress 
                  :percentage="getTaskProgress(task)" 
                  :status="getProgressStatus(task.status)"
                  :stroke-width="8"
                />
              </div>
              
              <div class="task-actions">
                <el-button-group size="small">
                  <el-button v-if="task.status === 'running'" @click="pauseTask(task)" type="warning">
                    <el-icon><VideoPause /></el-icon>
                    暂停
                  </el-button>
                  <el-button v-if="task.status === 'paused'" @click="resumeTask(task)" type="success">
                    <el-icon><VideoPlay /></el-icon>
                    恢复
                  </el-button>
                  <el-button @click="cancelTask(task)" type="danger">
                    <el-icon><Close /></el-icon>
                    取消
                  </el-button>
                  <el-button @click="viewTaskDetail(task)">
                    <el-icon><View /></el-icon>
                    详情
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：实时日志 -->
      <div class="panel-right">
        <div class="panel-card">
          <div class="panel-header">
            <h3>实时日志</h3>
            <div class="panel-actions">
              <el-tag :type="isWebSocketConnected ? 'success' : 'danger'" size="small">
                {{ isWebSocketConnected ? '已连接' : '未连接' }}
              </el-tag>
            </div>
          </div>
          
          <RealTimeLog height="600px" />
        </div>
      </div>
    </div>

    <!-- 性能指标 -->
    <div class="performance-metrics">
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-header">
            <h4>CPU使用率</h4>
            <el-icon><Cpu /></el-icon>
          </div>
          <div class="metric-value">
            <span class="value">{{ systemMetrics.cpu }}%</span>
            <el-progress :percentage="systemMetrics.cpu" :stroke-width="6" />
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h4>内存使用率</h4>
            <el-icon><MemoryCard /></el-icon>
          </div>
          <div class="metric-value">
            <span class="value">{{ systemMetrics.memory }}%</span>
            <el-progress :percentage="systemMetrics.memory" :stroke-width="6" />
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h4>网络IO</h4>
            <el-icon><Connection /></el-icon>
          </div>
          <div class="metric-value">
            <span class="value">{{ systemMetrics.network }} MB/s</span>
            <div class="metric-trend">
              <el-icon class="trend-up"><TrendCharts /></el-icon>
              <span>+12%</span>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h4>磁盘IO</h4>
            <el-icon><HardDrive /></el-icon>
          </div>
          <div class="metric-value">
            <span class="value">{{ systemMetrics.disk }} MB/s</span>
            <div class="metric-trend">
              <el-icon class="trend-down"><TrendCharts /></el-icon>
              <span>-5%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Monitor, Refresh, Timer, Clock, VideoPause, VideoPlay, Close, View,
  Cpu, MemoryCard, Connection, HardDrive, TrendCharts
} from '@element-plus/icons-vue'
import { useCrawlerStore } from '../stores/crawler'
import SystemStatus from '../components/SystemStatus.vue'
import RealTimeLog from '../components/RealTimeLog.vue'

const crawlerStore = useCrawlerStore()

// 响应式数据
const autoRefresh = ref(true)
const refreshTimer = ref<number | null>(null)
const systemMetrics = ref({
  cpu: 45,
  memory: 62,
  network: 12.5,
  disk: 8.3
})

// 计算属性
const activeTasks = computed(() =>
  crawlerStore.getAllTasks.filter(task => ['running', 'paused'].includes(task.status))
)

const isWebSocketConnected = computed(() => crawlerStore.isWebSocketConnected)

// 方法
const refreshAll = async () => {
  try {
    await crawlerStore.loadTasks()
    // 模拟刷新系统指标
    systemMetrics.value = {
      cpu: Math.floor(Math.random() * 100),
      memory: Math.floor(Math.random() * 100),
      network: Math.floor(Math.random() * 50 * 10) / 10,
      disk: Math.floor(Math.random() * 30 * 10) / 10
    }
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    startAutoRefresh()
    ElMessage.success('已开启自动刷新')
  } else {
    stopAutoRefresh()
    ElMessage.info('已停止自动刷新')
  }
}

const startAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
  
  refreshTimer.value = window.setInterval(() => {
    refreshAll()
  }, 10000) // 每10秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

const pauseTask = async (task: any) => {
  try {
    await crawlerStore.pauseTask(task.task_id)
    ElMessage.success('任务已暂停')
  } catch (error) {
    ElMessage.error('暂停任务失败')
  }
}

const resumeTask = async (task: any) => {
  try {
    await crawlerStore.resumeTask(task.task_id)
    ElMessage.success('任务已恢复')
  } catch (error) {
    ElMessage.error('恢复任务失败')
  }
}

const cancelTask = async (task: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 "${task.task_name}" 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await crawlerStore.cancelTask(task.task_id)
    ElMessage.success('任务已取消')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

const viewTaskDetail = (task: any) => {
  ElMessage.info('任务详情功能开发中...')
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    cancelled: 'warning',
    paused: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    paused: '已暂停'
  }
  return statusMap[status] || status
}

const getTaskProgress = (task: any) => {
  switch (task.status) {
    case 'completed':
      return 100
    case 'failed':
    case 'cancelled':
      return task.progress || 0
    case 'running':
      return task.progress || Math.floor(Math.random() * 50) + 30
    case 'paused':
      return task.progress || Math.floor(Math.random() * 30) + 10
    default:
      return 0
  }
}

const getProgressStatus = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'failed':
      return 'exception'
    case 'running':
      return undefined
    default:
      return undefined
  }
}

const getRunningTime = (task: any) => {
  if (!task.start_time) return '未开始'

  const startTime = new Date(task.start_time).getTime()
  const currentTime = Date.now()
  const diff = currentTime - startTime

  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 生命周期
onMounted(() => {
  refreshAll()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.real-time-monitoring {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
}

.title-icon {
  color: #3b82f6;
}

.page-description {
  margin: 0;
  color: #64748b;
  font-size: 16px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.system-overview {
  margin-bottom: 32px;
}

.monitoring-panels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.panel-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.panel-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.active-tasks-list {
  padding: 24px;
  max-height: 600px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #cbd5e1;
}

.active-task-item {
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.active-task-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-name {
  font-weight: 600;
  color: #1e293b;
}

.task-progress {
  margin-bottom: 16px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #64748b;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
}

.performance-metrics {
  background: #ffffff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.metric-card {
  padding: 24px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.metric-card:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.metric-header .el-icon {
  color: #64748b;
  font-size: 20px;
}

.metric-value {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.trend-up {
  color: #10b981;
}

.trend-down {
  color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .monitoring-panels {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .real-time-monitoring {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .task-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
