<template>
  <div class="crawler-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon class="title-icon"><Download /></el-icon>
          数据采集
        </h1>
        <p class="page-description">管理和监控所有爬虫任务的执行状态</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true" size="large">
          <el-icon><Plus /></el-icon>
          创建任务
        </el-button>
        <el-button @click="refreshTasks" size="large">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <div class="stats-grid">
        <div class="stat-card running">
          <div class="stat-icon">
            <el-icon><VideoPlay /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ runningTasks }}</div>
            <div class="stat-label">运行中</div>
            <div class="stat-trend">+2 今日</div>
          </div>
        </div>

        <div class="stat-card completed">
          <div class="stat-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ completedTasks }}</div>
            <div class="stat-label">已完成</div>
            <div class="stat-trend">+15 今日</div>
          </div>
        </div>

        <div class="stat-card failed">
          <div class="stat-icon">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ failedTasks }}</div>
            <div class="stat-label">失败</div>
            <div class="stat-trend">+1 今日</div>
          </div>
        </div>

        <div class="stat-card total">
          <div class="stat-icon">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ totalTasks }}</div>
            <div class="stat-label">总任务</div>
            <div class="stat-trend">{{ totalTasks }} 累计</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <div class="section-header">
        <h3>任务列表</h3>
        <div class="section-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索任务名称..."
            style="width: 200px; margin-right: 12px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-right: 12px">
            <el-option label="全部" value="" />
            <el-option label="运行中" value="running" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="已暂停" value="paused" />
          </el-select>
          <el-select v-model="typeFilter" placeholder="类型筛选" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="大学数据" value="university" />
            <el-option label="专业数据" value="major" />
          </el-select>
        </div>
      </div>

      <div class="task-table-container">
        <el-table
          :data="filteredTasks"
          style="width: 100%"
          :loading="loading"
          empty-text="暂无任务数据"
        >
          <el-table-column prop="task_name" label="任务名称" min-width="200">
            <template #default="{ row }">
              <div class="task-name-cell">
                <div class="task-name">{{ row.task_name }}</div>
                <div class="task-id">ID: {{ row.task_id.slice(0, 8) }}...</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="data_type" label="任务类型" width="120">
            <template #default="{ row }">
              <el-tag :type="row.data_type === 'university' ? 'primary' : 'success'" size="small">
                {{ row.data_type === 'university' ? '大学数据' : '专业数据' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="progress" label="进度" width="120">
            <template #default="{ row }">
              <el-progress
                :percentage="getTaskProgress(row)"
                :status="getProgressStatus(row.status)"
                :stroke-width="6"
              />
            </template>
          </el-table-column>

          <el-table-column prop="start_time" label="开始时间" width="160">
            <template #default="{ row }">
              {{ row.start_time ? formatTime(row.start_time) : '-' }}
            </template>
          </el-table-column>

          <el-table-column prop="end_time" label="结束时间" width="160">
            <template #default="{ row }">
              {{ row.end_time ? formatTime(row.end_time) : '-' }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="340" fixed="right">
            <template #default="{ row }">
              <el-button-group size="small">
                <el-button @click="showTaskLogs(row)" title="查看日志">
                  <el-icon><Document /></el-icon>
                </el-button>
                <el-button v-if="row.status === 'running'" @click="pauseTask(row)" type="warning" title="暂停">
                  <el-icon><VideoPause /></el-icon>
                </el-button>
                <el-button v-if="row.status === 'paused'" @click="resumeTask(row)" type="success" title="恢复">
                  <el-icon><VideoPlay /></el-icon>
                </el-button>
                <el-button v-if="['running', 'paused'].includes(row.status)" @click="cancelTask(row)" type="danger" title="取消">
                  <el-icon><Close /></el-icon>
                </el-button>
                <el-button v-if="['completed', 'failed', 'cancelled'].includes(row.status)" @click="restartTask(row)" type="primary" title="重启">
                  <el-icon><Refresh /></el-icon>
                </el-button>
                <el-button @click="viewTaskDetail(row)" title="详情">
                  <el-icon><View /></el-icon>
                </el-button>
                <el-button @click="deleteTask(row)" type="danger" title="删除">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredTasks.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建任务对话框 -->
    <CreateTaskDialog
      v-model="showCreateDialog"
      @task-created="handleTaskCreated"
    />

    <!-- 任务日志对话框 -->
    <TaskLogDialog
      v-model="showLogDialog"
      :task="selectedTask"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download, Plus, Refresh, VideoPlay, CircleCheck, CircleClose, DataBoard,
  Search, VideoPause, Close, View, Delete, Document
} from '@element-plus/icons-vue'
import { useCrawlerStore } from '../stores/crawler'
import CreateTaskDialog from '../components/CreateTaskDialog.vue'
import TaskLogDialog from '../components/TaskLogDialog.vue'

const crawlerStore = useCrawlerStore()

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const showLogDialog = ref(false)
const selectedTask = ref<any>(null)
const searchKeyword = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const filteredTasks = computed(() => {
  let tasks = crawlerStore.getAllTasks

  // 搜索过滤
  if (searchKeyword.value) {
    tasks = tasks.filter(task =>
      task.task_name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    tasks = tasks.filter(task => task.status === statusFilter.value)
  }

  // 类型过滤
  if (typeFilter.value) {
    tasks = tasks.filter(task => task.data_type === typeFilter.value)
  }

  return tasks
})

const runningTasks = computed(() =>
  crawlerStore.getAllTasks.filter(task => task.status === 'running').length
)

const completedTasks = computed(() =>
  crawlerStore.getAllTasks.filter(task => task.status === 'completed').length
)

const failedTasks = computed(() =>
  crawlerStore.getAllTasks.filter(task => task.status === 'failed').length
)

const totalTasks = computed(() => crawlerStore.getAllTasks.length)

// 方法
const refreshTasks = async () => {
  loading.value = true
  try {
    await crawlerStore.loadTasks()
    ElMessage.success('任务列表刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const showTaskLogs = (task: any) => {
  selectedTask.value = task
  showLogDialog.value = true
}

const pauseTask = async (task: any) => {
  try {
    await crawlerStore.pauseTask(task.task_id)
    await refreshTasks()
    ElMessage.success('任务已暂停')
  } catch (error) {
    ElMessage.error('暂停任务失败')
  }
}

const resumeTask = async (task: any) => {
  try {
    await crawlerStore.resumeTask(task.task_id)
    await refreshTasks()
    ElMessage.success('任务已恢复')
  } catch (error) {
    ElMessage.error('恢复任务失败')
  }
}

const cancelTask = async (task: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 "${task.task_name}" 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await crawlerStore.cancelTask(task.task_id)
    await refreshTasks()
    ElMessage.success('任务已取消')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

const restartTask = async (task: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要重启任务 "${task.task_name}" 吗？`,
      '确认重启',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    // 这里需要实现重启任务的API
    ElMessage.success('任务重启功能开发中...')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重启任务失败')
    }
  }
}

const deleteTask = async (task: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${task.task_name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await crawlerStore.deleteTask(task.task_id)
    await refreshTasks()
    ElMessage.success('任务已删除')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

const viewTaskDetail = (task: any) => {
  // 实现任务详情查看
  ElMessage.info('任务详情功能开发中...')
}

const handleTaskCreated = () => {
  showCreateDialog.value = false
  refreshTasks()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    cancelled: 'warning',
    paused: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    paused: '已暂停'
  }
  return statusMap[status] || status
}

const getTaskProgress = (task: any) => {
  // 根据任务状态返回进度
  switch (task.status) {
    case 'completed':
      return 100
    case 'failed':
    case 'cancelled':
      return task.progress || 0
    case 'running':
      return task.progress || 50
    case 'paused':
      return task.progress || 30
    default:
      return 0
  }
}

const getProgressStatus = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'failed':
      return 'exception'
    case 'running':
      return undefined
    default:
      return undefined
  }
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 生命周期
onMounted(async () => {
  try {
    await crawlerStore.connectWebSocket()
    await refreshTasks()
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style scoped>
.crawler-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
}

.title-icon {
  color: #3b82f6;
}

.page-description {
  margin: 0;
  color: #64748b;
  font-size: 16px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-overview {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.stat-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.running {
  border-left-color: #3b82f6;
}

.stat-card.completed {
  border-left-color: #10b981;
}

.stat-card.failed {
  border-left-color: #ef4444;
}

.stat-card.total {
  border-left-color: #8b5cf6;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-card.running .stat-icon {
  background: #dbeafe;
  color: #3b82f6;
}

.stat-card.completed .stat-icon {
  background: #d1fae5;
  color: #10b981;
}

.stat-card.failed .stat-icon {
  background: #fee2e2;
  color: #ef4444;
}

.stat-card.total .stat-icon {
  background: #ede9fe;
  color: #8b5cf6;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.task-list-section {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e2e8f0;
}

.section-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.section-actions {
  display: flex;
  align-items: center;
}

.task-table-container {
  padding: 0 32px;
}

.task-name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-name {
  font-weight: 500;
  color: #1e293b;
}

.task-id {
  font-size: 12px;
  color: #64748b;
  font-family: monospace;
}

.pagination-container {
  padding: 24px 32px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: center;
}
</style>
