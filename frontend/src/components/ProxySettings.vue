<template>
  <div class="proxy-settings">
    <el-form :model="proxyForm" label-width="120px">
      <el-form-item label="启用代理">
        <el-switch v-model="proxyForm.enabled" />
      </el-form-item>
      
      <template v-if="proxyForm.enabled">
        <el-form-item label="代理类型">
          <el-select v-model="proxyForm.type" placeholder="选择代理类型">
            <el-option label="HTTP" value="http" />
            <el-option label="HTTPS" value="https" />
            <el-option label="SOCKS5" value="socks5" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="代理地址">
          <el-input v-model="proxyForm.host" placeholder="127.0.0.1" />
        </el-form-item>
        
        <el-form-item label="端口">
          <el-input-number v-model="proxyForm.port" :min="1" :max="65535" placeholder="8080" />
        </el-form-item>
        
        <el-form-item label="需要认证">
          <el-switch v-model="proxyForm.needAuth" />
        </el-form-item>
        
        <template v-if="proxyForm.needAuth">
          <el-form-item label="用户名">
            <el-input v-model="proxyForm.username" placeholder="用户名" />
          </el-form-item>
          
          <el-form-item label="密码">
            <el-input v-model="proxyForm.password" type="password" placeholder="密码" show-password />
          </el-form-item>
        </template>
      </template>
      
      <el-form-item>
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
        <el-button @click="testConnection">测试连接</el-button>
        <el-button @click="resetSettings">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 代理状态 -->
    <el-card v-if="proxyForm.enabled" class="status-card">
      <template #header>
        <span>代理状态</span>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="状态">
          <el-tag :type="connectionStatus === 'connected' ? 'success' : 'danger'">
            {{ connectionStatus === 'connected' ? '已连接' : '未连接' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="延迟">
          {{ latency }}ms
        </el-descriptions-item>
        <el-descriptions-item label="代理地址">
          {{ `${proxyForm.type}://${proxyForm.host}:${proxyForm.port}` }}
        </el-descriptions-item>
        <el-descriptions-item label="最后测试">
          {{ lastTestTime }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const proxyForm = ref({
  enabled: false,
  type: 'http',
  host: '',
  port: 8080,
  needAuth: false,
  username: '',
  password: ''
})

const connectionStatus = ref('disconnected')
const latency = ref(0)
const lastTestTime = ref('-')

// 方法
const saveSettings = async () => {
  try {
    // 这里应该调用API保存代理设置
    ElMessage.success('代理设置已保存')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const testConnection = async () => {
  if (!proxyForm.value.enabled) {
    ElMessage.warning('请先启用代理')
    return
  }
  
  if (!proxyForm.value.host || !proxyForm.value.port) {
    ElMessage.warning('请填写代理地址和端口')
    return
  }
  
  try {
    ElMessage.info('正在测试代理连接...')
    
    // 模拟测试连接
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 随机生成测试结果
    const isSuccess = Math.random() > 0.3
    
    if (isSuccess) {
      connectionStatus.value = 'connected'
      latency.value = Math.floor(Math.random() * 200 + 50)
      lastTestTime.value = new Date().toLocaleString()
      ElMessage.success('代理连接测试成功')
    } else {
      connectionStatus.value = 'disconnected'
      ElMessage.error('代理连接测试失败')
    }
  } catch (error) {
    connectionStatus.value = 'disconnected'
    ElMessage.error('测试连接失败')
  }
}

const resetSettings = () => {
  proxyForm.value = {
    enabled: false,
    type: 'http',
    host: '',
    port: 8080,
    needAuth: false,
    username: '',
    password: ''
  }
  connectionStatus.value = 'disconnected'
  latency.value = 0
  lastTestTime.value = '-'
  ElMessage.success('设置已重置')
}

const loadSettings = async () => {
  try {
    // 这里应该调用API加载代理设置
    // const settings = await api.getProxySettings()
    // proxyForm.value = settings
  } catch (error) {
    console.error('加载代理设置失败:', error)
  }
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.proxy-settings {
  padding: 20px;
}

.status-card {
  margin-top: 20px;
}
</style>
