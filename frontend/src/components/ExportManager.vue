<template>
  <div class="export-manager">
    <el-tabs v-model="activeTab">
      <!-- 数据导出 -->
      <el-tab-pane label="数据导出" name="export">
        <el-form :model="exportForm" label-width="120px">
          <el-form-item label="数据类型">
            <el-select v-model="exportForm.dataType" placeholder="选择数据类型">
              <el-option label="大学数据" value="university" />
              <el-option label="专业数据" value="major" />
              <el-option label="录取数据" value="admission" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="导出格式">
            <el-select v-model="exportForm.format" placeholder="选择导出格式">
              <el-option label="Excel (.xlsx)" value="xlsx" />
              <el-option label="CSV (.csv)" value="csv" />
              <el-option label="JSON (.json)" value="json" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="年份筛选">
            <el-select v-model="exportForm.years" multiple placeholder="选择年份">
              <el-option label="2022" value="2022" />
              <el-option label="2023" value="2023" />
              <el-option label="2024" value="2024" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="省份筛选">
            <el-select v-model="exportForm.provinces" multiple placeholder="选择省份">
              <el-option label="北京" value="北京" />
              <el-option label="上海" value="上海" />
              <el-option label="广东" value="广东" />
              <el-option label="浙江" value="浙江" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="startExport" :loading="exporting">
              <el-icon><Download /></el-icon>
              开始导出
            </el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <!-- 导出历史 -->
      <el-tab-pane label="导出历史" name="history">
        <el-table :data="exportHistory" style="width: 100%">
          <el-table-column prop="fileName" label="文件名" />
          <el-table-column prop="dataType" label="数据类型" width="120">
            <template #default="{ row }">
              <el-tag>{{ getDataTypeText(row.dataType) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="format" label="格式" width="80" />
          <el-table-column prop="size" label="大小" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button-group size="small">
                <el-button v-if="row.status === 'completed'" @click="downloadFile(row)">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
                <el-button @click="deleteExport(row)" type="danger">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Delete } from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('export')
const exporting = ref(false)

const exportForm = ref({
  dataType: '',
  format: '',
  years: [],
  provinces: []
})

const exportHistory = ref([
  {
    id: 1,
    fileName: '大学数据_2024_01_19.xlsx',
    dataType: 'university',
    format: 'xlsx',
    size: '2.5MB',
    status: 'completed',
    createTime: '2024-01-19 14:30:00'
  },
  {
    id: 2,
    fileName: '专业数据_2024_01_18.csv',
    dataType: 'major',
    format: 'csv',
    size: '1.8MB',
    status: 'completed',
    createTime: '2024-01-18 16:45:00'
  },
  {
    id: 3,
    fileName: '录取数据_2024_01_17.json',
    dataType: 'admission',
    format: 'json',
    size: '3.2MB',
    status: 'failed',
    createTime: '2024-01-17 10:20:00'
  }
])

// 方法
const startExport = async () => {
  if (!exportForm.value.dataType || !exportForm.value.format) {
    ElMessage.warning('请选择数据类型和导出格式')
    return
  }
  
  try {
    exporting.value = true
    ElMessage.info('开始导出数据...')
    
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 添加到导出历史
    const newExport = {
      id: Date.now(),
      fileName: `${getDataTypeText(exportForm.value.dataType)}_${new Date().toISOString().slice(0, 10)}.${exportForm.value.format}`,
      dataType: exportForm.value.dataType,
      format: exportForm.value.format,
      size: `${(Math.random() * 5 + 1).toFixed(1)}MB`,
      status: 'completed',
      createTime: new Date().toLocaleString()
    }
    
    exportHistory.value.unshift(newExport)
    ElMessage.success('数据导出完成')
    activeTab.value = 'history'
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const downloadFile = (row: any) => {
  // 模拟文件下载
  const link = document.createElement('a')
  link.href = '#'
  link.download = row.fileName
  link.click()
  ElMessage.success(`开始下载 ${row.fileName}`)
}

const deleteExport = (row: any) => {
  const index = exportHistory.value.findIndex(item => item.id === row.id)
  if (index > -1) {
    exportHistory.value.splice(index, 1)
    ElMessage.success('导出记录已删除')
  }
}

const resetForm = () => {
  exportForm.value = {
    dataType: '',
    format: '',
    years: [],
    provinces: []
  }
}

const getDataTypeText = (type: string) => {
  const map: Record<string, string> = {
    university: '大学数据',
    major: '专业数据',
    admission: '录取数据'
  }
  return map[type] || type
}

const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    completed: 'success',
    failed: 'danger',
    processing: 'warning'
  }
  return map[status] || 'info'
}

const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    completed: '已完成',
    failed: '失败',
    processing: '处理中'
  }
  return map[status] || status
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.export-manager {
  padding: 20px;
}
</style>
