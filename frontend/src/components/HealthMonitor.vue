<template>
  <div class="health-monitor">
    <!-- 系统状态概览 -->
    <el-row :gutter="20" class="status-overview">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon cpu">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ systemStats.cpu }}%</div>
              <div class="status-label">CPU使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon memory">
              <el-icon><DataBoard /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ systemStats.memory }}%</div>
              <div class="status-label">内存使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon network">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ systemStats.network }}ms</div>
              <div class="status-label">网络延迟</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon tasks">
              <el-icon><List /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ systemStats.activeTasks }}</div>
              <div class="status-label">活跃任务</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 服务状态 -->
    <el-card class="service-status">
      <template #header>
        <span>服务状态</span>
        <el-button @click="refreshStatus" size="small" style="float: right;">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </template>
      
      <el-table :data="services" style="width: 100%">
        <el-table-column prop="name" label="服务名称" width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'running' ? 'success' : 'danger'">
              {{ row.status === 'running' ? '运行中' : '已停止' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="uptime" label="运行时间" width="150" />
        <el-table-column prop="memory" label="内存占用" width="120" />
        <el-table-column prop="cpu" label="CPU占用" width="120" />
        <el-table-column prop="lastCheck" label="最后检查" width="180" />
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button v-if="row.status === 'stopped'" @click="startService(row)" type="success">
                启动
              </el-button>
              <el-button v-if="row.status === 'running'" @click="stopService(row)" type="warning">
                停止
              </el-button>
              <el-button @click="restartService(row)">重启</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 系统日志 -->
    <el-card class="system-logs">
      <template #header>
        <span>系统日志</span>
        <el-button @click="clearLogs" size="small" style="float: right;">
          <el-icon><Delete /></el-icon>
          清空日志
        </el-button>
      </template>
      
      <div class="log-container">
        <div v-for="(log, index) in systemLogs" :key="index" :class="['log-entry', `log-${log.level}`]">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Monitor, DataBoard, Connection, List, Refresh, Delete } from '@element-plus/icons-vue'

// 响应式数据
const systemStats = ref({
  cpu: 45,
  memory: 68,
  network: 23,
  activeTasks: 3
})

const services = ref([
  {
    id: 1,
    name: '爬虫服务',
    status: 'running',
    uptime: '2天 14小时',
    memory: '256MB',
    cpu: '12%',
    lastCheck: '2024-01-19 15:30:00'
  },
  {
    id: 2,
    name: '数据库服务',
    status: 'running',
    uptime: '5天 8小时',
    memory: '512MB',
    cpu: '8%',
    lastCheck: '2024-01-19 15:30:00'
  },
  {
    id: 3,
    name: 'WebSocket服务',
    status: 'running',
    uptime: '1天 6小时',
    memory: '128MB',
    cpu: '3%',
    lastCheck: '2024-01-19 15:30:00'
  }
])

const systemLogs = ref([
  {
    time: '15:30:25',
    level: 'info',
    message: '爬虫任务执行完成，共处理 150 条记录'
  },
  {
    time: '15:28:12',
    level: 'warning',
    message: '数据库连接池使用率达到 80%'
  },
  {
    time: '15:25:45',
    level: 'info',
    message: '新的爬虫任务已启动'
  },
  {
    time: '15:20:33',
    level: 'error',
    message: '网络请求超时，正在重试...'
  }
])

let refreshTimer: number | null = null

// 方法
const refreshStatus = async () => {
  try {
    // 模拟刷新系统状态
    systemStats.value = {
      cpu: Math.floor(Math.random() * 100),
      memory: Math.floor(Math.random() * 100),
      network: Math.floor(Math.random() * 100 + 10),
      activeTasks: Math.floor(Math.random() * 10)
    }
    
    // 更新服务状态
    services.value.forEach(service => {
      service.lastCheck = new Date().toLocaleString()
      service.cpu = `${Math.floor(Math.random() * 20)}%`
    })
    
    ElMessage.success('状态已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

const startService = (service: any) => {
  service.status = 'running'
  service.uptime = '刚刚启动'
  ElMessage.success(`${service.name} 已启动`)
}

const stopService = (service: any) => {
  service.status = 'stopped'
  service.uptime = '-'
  ElMessage.warning(`${service.name} 已停止`)
}

const restartService = (service: any) => {
  ElMessage.info(`正在重启 ${service.name}...`)
  setTimeout(() => {
    service.status = 'running'
    service.uptime = '刚刚重启'
    ElMessage.success(`${service.name} 重启完成`)
  }, 2000)
}

const clearLogs = () => {
  systemLogs.value = []
  ElMessage.success('日志已清空')
}

const addLog = (level: string, message: string) => {
  const now = new Date()
  systemLogs.value.unshift({
    time: now.toLocaleTimeString(),
    level,
    message
  })
  
  // 保持最多50条日志
  if (systemLogs.value.length > 50) {
    systemLogs.value = systemLogs.value.slice(0, 50)
  }
}

onMounted(() => {
  // 定时刷新状态
  refreshTimer = window.setInterval(() => {
    refreshStatus()
  }, 30000) // 30秒刷新一次
  
  // 模拟日志生成
  setInterval(() => {
    const messages = [
      '系统运行正常',
      '数据同步完成',
      '新用户访问',
      '缓存已更新',
      '定时任务执行'
    ]
    const levels = ['info', 'warning', 'error']
    const message = messages[Math.floor(Math.random() * messages.length)]
    const level = levels[Math.floor(Math.random() * levels.length)]
    addLog(level, message)
  }, 10000) // 10秒生成一条日志
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.health-monitor {
  padding: 20px;
}

.status-overview {
  margin-bottom: 20px;
}

.status-card {
  height: 100px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 15px;
}

.status-icon.cpu {
  background: #e3f2fd;
  color: #2196f3;
}

.status-icon.memory {
  background: #f3e5f5;
  color: #9c27b0;
}

.status-icon.network {
  background: #e8f5e8;
  color: #4caf50;
}

.status-icon.tasks {
  background: #fff3e0;
  color: #ff9800;
}

.status-content {
  flex: 1;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.status-label {
  font-size: 14px;
  color: #909399;
}

.service-status,
.system-logs {
  margin-bottom: 20px;
}

.log-container {
  height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-entry {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  width: 80px;
  color: #909399;
}

.log-level {
  width: 60px;
  font-weight: bold;
}

.log-entry.log-info .log-level {
  color: #409eff;
}

.log-entry.log-warning .log-level {
  color: #e6a23c;
}

.log-entry.log-error .log-level {
  color: #f56c6c;
}

.log-message {
  flex: 1;
  color: #303133;
}
</style>
