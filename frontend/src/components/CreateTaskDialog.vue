<template>
  <el-dialog
    v-model="visible"
    title="创建爬虫任务"
    width="800px"
    :before-close="handleClose"
    class="create-task-dialog"
    align-center
  >
    <div class="dialog-content">
      <!-- 任务配置表单 -->
      <div class="form-section">
        <h4 class="section-title">任务配置</h4>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          @submit.prevent
          class="task-form"
        >
          <el-form-item label="任务类型" prop="taskType" required>
            <el-radio-group v-model="form.taskType" class="task-type-group">
              <el-radio-button value="university">
                <el-icon><School /></el-icon>
                大学数据爬取
              </el-radio-button>
              <el-radio-button value="major">
                <el-icon><Reading /></el-icon>
                专业数据爬取
              </el-radio-button>
            </el-radio-group>
          </el-form-item>

      <el-form-item label="目标年份" prop="years">
        <el-select
          v-model="form.years"
          multiple
          placeholder="选择年份（默认全部）"
          style="width: 100%"
        >
          <el-option
            v-for="year in availableYears"
            :key="year"
            :label="year"
            :value="year"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="目标省份" prop="provinces">
        <el-select
          v-model="form.provinces"
          multiple
          placeholder="选择省份（默认全部）"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="province in availableProvinces"
            :key="province"
            :label="province"
            :value="province"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="科类类型" prop="scienceTypes">
        <el-select
          v-model="form.scienceTypes"
          multiple
          placeholder="选择科类（默认全部）"
          style="width: 100%"
        >
          <el-option
            v-for="(name, code) in availableScienceTypes"
            :key="code"
            :label="name"
            :value="code"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item 
        v-if="form.taskType === 'university'" 
        label="批次类型" 
        prop="batchTypes"
      >
        <el-select
          v-model="form.batchTypes"
          multiple
          placeholder="选择批次（默认全部）"
          style="width: 100%"
        >
          <el-option
            v-for="(name, code) in availableBatchTypes"
            :key="code"
            :label="name"
            :value="code"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item 
        v-if="form.taskType === 'major'" 
        label="大学代码" 
        prop="universityCodes"
      >
        <el-input
          v-model="form.universityCodesText"
          type="textarea"
          :rows="3"
          placeholder="输入大学代码，多个用逗号分隔（留空则爬取所有大学）"
        />
        <div class="form-tip">
          示例: 1103,1104,1105 或留空爬取所有大学
        </div>
      </el-form-item>
    </el-form>
      </div>
    </div>
    
    <div class="task-preview">
      <h4>任务预览</h4>
      <el-descriptions :column="2" border size="small">
        <el-descriptions-item label="任务类型">
          {{ form.taskType === 'university' ? '大学数据爬取' : '专业数据爬取' }}
        </el-descriptions-item>
        <el-descriptions-item label="年份数量">
          {{ form.years.length || '全部' }} 个年份
        </el-descriptions-item>
        <el-descriptions-item label="省份数量">
          {{ form.provinces.length || '全部' }} 个省份
        </el-descriptions-item>
        <el-descriptions-item label="科类数量">
          {{ form.scienceTypes.length || '全部' }} 个科类
        </el-descriptions-item>
        <el-descriptions-item v-if="form.taskType === 'university'" label="批次数量">
          {{ form.batchTypes.length || '全部' }} 个批次
        </el-descriptions-item>
        <el-descriptions-item v-if="form.taskType === 'major'" label="大学数量">
          {{ universityCodes.length || '全部' }} 所大学
        </el-descriptions-item>
      </el-descriptions>
      
      <el-alert
        v-if="estimatedRecords > 0"
        :title="`预计爬取约 ${estimatedRecords.toLocaleString()} 条记录`"
        type="info"
        :closable="false"
        show-icon
        style="margin-top: 12px"
      />
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          创建任务
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { School, Reading, Plus } from '@element-plus/icons-vue'
import { useCrawlerStore } from '../stores/crawler'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'task-created', task: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const crawlerStore = useCrawlerStore()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

const form = ref({
  taskType: 'university' as 'university' | 'major',
  years: [] as number[],
  provinces: [] as string[],
  scienceTypes: [] as string[],
  batchTypes: [] as string[],
  universityCodesText: ''
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const availableYears = computed(() => {
  return crawlerStore.config?.data?.target_years || []
})

const availableProvinces = computed(() => {
  return crawlerStore.config?.data?.provinces || []
})

const availableScienceTypes = computed(() => {
  return crawlerStore.config?.data?.science_types || {}
})

const availableBatchTypes = computed(() => {
  return crawlerStore.config?.data?.batch_types || {}
})

const universityCodes = computed(() => {
  if (!form.value.universityCodesText.trim()) return []
  return form.value.universityCodesText
    .split(',')
    .map(code => code.trim())
    .filter(code => code)
})

const estimatedRecords = computed(() => {
  const years = form.value.years.length || availableYears.value.length
  const provinces = form.value.provinces.length || availableProvinces.value.length
  const scienceTypes = form.value.scienceTypes.length || Object.keys(availableScienceTypes.value).length
  
  if (form.value.taskType === 'university') {
    const batches = form.value.batchTypes.length || Object.keys(availableBatchTypes.value).length
    return years * provinces * scienceTypes * batches * 50 // 估算每个组合50条记录
  } else {
    const universities = universityCodes.value.length || 1000 // 估算1000所大学
    return years * provinces * scienceTypes * universities * 10 // 估算每所大学10个专业
  }
})

// 表单验证规则
const rules: FormRules = {
  taskType: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ]
}

// 方法
const handleClose = () => {
  resetForm()
  visible.value = false
}

const resetForm = () => {
  form.value = {
    taskType: 'university',
    years: [],
    provinces: [],
    scienceTypes: [],
    batchTypes: [],
    universityCodesText: ''
  }
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const params: any = {
      years: form.value.years.length > 0 ? form.value.years : undefined,
      provinces: form.value.provinces.length > 0 ? form.value.provinces : undefined,
      science_types: form.value.scienceTypes.length > 0 ? form.value.scienceTypes : undefined
    }
    
    if (form.value.taskType === 'university') {
      params.batch_types = form.value.batchTypes.length > 0 ? form.value.batchTypes : undefined
      const response = await crawlerStore.startUniversityCrawl(params)
      emit('task-created', response)
    } else {
      params.university_codes = universityCodes.value.length > 0 ? universityCodes.value : undefined
      const response = await crawlerStore.startMajorCrawl(params)
      emit('task-created', response)
    }
    
    handleClose()
    
  } catch (error) {
    console.error('创建任务失败:', error)
  } finally {
    submitting.value = false
  }
}

// 监听任务类型变化，重置相关字段
watch(() => form.value.taskType, () => {
  form.value.batchTypes = []
  form.value.universityCodesText = ''
})

// 组件挂载时加载配置
onMounted(async () => {
  if (!crawlerStore.config) {
    await crawlerStore.loadConfig()
  }
})
</script>

<style scoped>
.create-task-dialog :deep(.el-dialog) {
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e2e8f0;
}

.create-task-dialog :deep(.el-dialog__header) {
  background: #f8fafc;
  color: #1e293b;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.create-task-dialog :deep(.el-dialog__title) {
  font-size: 20px;
  font-weight: 600;
}

.dialog-content {
  padding: 24px;
}

.form-section, .preview-section {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::before {
  content: '';
  width: 3px;
  height: 16px;
  background: #3b82f6;
  border-radius: 2px;
}

.task-form {
  margin-top: 8px;
}

.task-type-group {
  width: 100%;
}

.task-type-group .el-radio-button {
  flex: 1;
}

.task-type-group .el-radio-button__inner {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.task-type-group .el-radio-button__inner:hover {
  border-color: #cbd5e1;
  background: #f1f5f9;
}

.preview-card {
  background: #ffffff;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.preview-item:last-child {
  border-bottom: none;
}

.preview-label {
  font-weight: 500;
  color: #64748b;
  font-size: 14px;
}

.preview-value {
  color: #1e293b;
  font-weight: 500;
  font-size: 14px;
}

.estimate-info {
  margin-top: 16px;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.task-preview {
  margin-top: 20px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

.task-preview h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  background: #f8fafc;
  border-radius: 0 0 12px 12px;
  border-top: 1px solid #e2e8f0;
}

.dialog-footer .el-button {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.dialog-footer .el-button:hover {
  border-color: #cbd5e1;
}

.dialog-footer .el-button--primary {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.dialog-footer .el-button--primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* 表单项样式优化 */
.task-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

.task-form :deep(.el-select),
.task-form :deep(.el-input) {
  border-radius: 8px;
}

.task-form :deep(.el-select__wrapper),
.task-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.task-form :deep(.el-select__wrapper:hover),
.task-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-content > * {
  animation: slideInUp 0.5s ease-out;
}

.form-section {
  animation-delay: 0.1s;
}

.preview-section {
  animation-delay: 0.2s;
}
</style>
