<template>
  <div class="schedule-manager">
    <div class="schedule-header">
      <h3>定时任务管理</h3>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新建定时任务
      </el-button>
    </div>

    <!-- 定时任务列表 -->
    <el-table :data="schedules" style="width: 100%">
      <el-table-column prop="name" label="任务名称" width="200" />
      <el-table-column prop="type" label="任务类型" width="120">
        <template #default="{ row }">
          <el-tag :type="row.type === 'university' ? 'primary' : 'success'">
            {{ row.type === 'university' ? '大学数据' : '专业数据' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="schedule" label="执行计划" width="150" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.enabled ? 'success' : 'info'">
            {{ row.enabled ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="nextRun" label="下次执行" width="180" />
      <el-table-column prop="lastRun" label="上次执行" width="180" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button-group size="small">
            <el-button @click="toggleSchedule(row)" :type="row.enabled ? 'warning' : 'success'">
              {{ row.enabled ? '禁用' : '启用' }}
            </el-button>
            <el-button @click="editSchedule(row)">编辑</el-button>
            <el-button @click="deleteSchedule(row)" type="danger">删除</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建定时任务对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建定时任务" width="600px">
      <el-form :model="scheduleForm" label-width="100px">
        <el-form-item label="任务名称">
          <el-input v-model="scheduleForm.name" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务类型">
          <el-select v-model="scheduleForm.type" placeholder="选择任务类型">
            <el-option label="大学数据" value="university" />
            <el-option label="专业数据" value="major" />
          </el-select>
        </el-form-item>
        <el-form-item label="执行计划">
          <el-select v-model="scheduleForm.scheduleType" placeholder="选择执行计划">
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
            <el-option label="自定义Cron" value="cron" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="scheduleForm.scheduleType === 'daily'" label="执行时间">
          <el-time-picker v-model="scheduleForm.time" placeholder="选择时间" />
        </el-form-item>
        <el-form-item v-if="scheduleForm.scheduleType === 'weekly'" label="星期">
          <el-select v-model="scheduleForm.weekday" placeholder="选择星期">
            <el-option label="星期一" :value="1" />
            <el-option label="星期二" :value="2" />
            <el-option label="星期三" :value="3" />
            <el-option label="星期四" :value="4" />
            <el-option label="星期五" :value="5" />
            <el-option label="星期六" :value="6" />
            <el-option label="星期日" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="scheduleForm.scheduleType === 'cron'" label="Cron表达式">
          <el-input v-model="scheduleForm.cron" placeholder="0 0 * * *" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createSchedule">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const showCreateDialog = ref(false)
const schedules = ref([
  {
    id: 1,
    name: '每日大学数据更新',
    type: 'university',
    schedule: '每日 02:00',
    enabled: true,
    nextRun: '2024-01-20 02:00:00',
    lastRun: '2024-01-19 02:00:00'
  },
  {
    id: 2,
    name: '周末专业数据同步',
    type: 'major',
    schedule: '每周日 03:00',
    enabled: false,
    nextRun: '2024-01-21 03:00:00',
    lastRun: '2024-01-14 03:00:00'
  }
])

const scheduleForm = ref({
  name: '',
  type: '',
  scheduleType: '',
  time: null,
  weekday: null,
  cron: ''
})

// 方法
const toggleSchedule = (schedule: any) => {
  schedule.enabled = !schedule.enabled
  ElMessage.success(`定时任务已${schedule.enabled ? '启用' : '禁用'}`)
}

const editSchedule = (schedule: any) => {
  ElMessage.info('编辑功能开发中...')
}

const deleteSchedule = (schedule: any) => {
  const index = schedules.value.findIndex(s => s.id === schedule.id)
  if (index > -1) {
    schedules.value.splice(index, 1)
    ElMessage.success('定时任务已删除')
  }
}

const createSchedule = () => {
  // 这里应该调用API创建定时任务
  const newSchedule = {
    id: Date.now(),
    name: scheduleForm.value.name,
    type: scheduleForm.value.type,
    schedule: getScheduleText(),
    enabled: true,
    nextRun: '计算中...',
    lastRun: '-'
  }
  
  schedules.value.push(newSchedule)
  showCreateDialog.value = false
  resetForm()
  ElMessage.success('定时任务创建成功')
}

const getScheduleText = () => {
  const form = scheduleForm.value
  switch (form.scheduleType) {
    case 'daily':
      return `每日 ${form.time ? new Date(form.time).toLocaleTimeString() : '00:00'}`
    case 'weekly':
      const weekdays = ['日', '一', '二', '三', '四', '五', '六']
      return `每周${weekdays[form.weekday || 0]}`
    case 'monthly':
      return '每月1日'
    case 'cron':
      return form.cron || '自定义'
    default:
      return '未设置'
  }
}

const resetForm = () => {
  scheduleForm.value = {
    name: '',
    type: '',
    scheduleType: '',
    time: null,
    weekday: null,
    cron: ''
  }
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.schedule-manager {
  padding: 20px;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.schedule-header h3 {
  margin: 0;
  color: #303133;
}
</style>
