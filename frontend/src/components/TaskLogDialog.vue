<template>
  <el-dialog
    v-model="visible"
    :title="`任务日志 - ${task?.task_name || '未知任务'}`"
    width="80%"
    top="5vh"
    class="task-log-dialog"
    :before-close="handleClose"
  >
    <div class="log-container">
      <!-- 日志工具栏 -->
      <div class="log-toolbar">
        <div class="toolbar-left">
          <el-space>
            <el-tag :type="getStatusType(task?.status)">
              {{ getStatusText(task?.status) }}
            </el-tag>
            <span class="task-info">
              任务ID: {{ task?.task_id }}
            </span>
            <span class="task-info">
              类型: {{ task?.data_type === 'university' ? '大学数据' : '专业数据' }}
            </span>
          </el-space>
        </div>
        <div class="toolbar-right">
          <el-space>
            <el-select v-model="logLevel" placeholder="日志级别" size="small" style="width: 100px">
              <el-option label="全部" value="" />
              <el-option label="INFO" value="info" />
              <el-option label="WARNING" value="warning" />
              <el-option label="ERROR" value="error" />
            </el-select>
            <el-button @click="refreshLogs" size="small" :loading="refreshing">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="clearLogs" size="small">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
            <el-button @click="downloadLogs" size="small">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-switch v-model="autoScroll" active-text="自动滚动" size="small" />
          </el-space>
        </div>
      </div>

      <!-- 日志内容 -->
      <div class="log-content" ref="logContainer">
        <div v-if="filteredLogs.length === 0" class="empty-logs">
          <el-empty description="暂无日志数据" />
        </div>
        <div v-else class="log-lines">
          <div
            v-for="(log, index) in filteredLogs"
            :key="index"
            :class="['log-line', `log-${log.level}`]"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>

      <!-- 日志统计 -->
      <div class="log-stats">
        <el-space>
          <span>总计: {{ logs.length }} 条</span>
          <span>INFO: {{ logStats.info }} 条</span>
          <span>WARNING: {{ logStats.warning }} 条</span>
          <span>ERROR: {{ logStats.error }} 条</span>
        </el-space>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Delete, Download } from '@element-plus/icons-vue'
import { useCrawlerStore } from '../stores/crawler'

interface LogEntry {
  timestamp: number | string
  level: 'info' | 'warning' | 'error'
  message: string
}

interface Props {
  modelValue: boolean
  task: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const crawlerStore = useCrawlerStore()

// 响应式数据
const logContainer = ref<HTMLElement>()
const refreshing = ref(false)
const autoScroll = ref(true)
const logLevel = ref('')
const logs = ref<LogEntry[]>([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const filteredLogs = computed(() => {
  if (!logLevel.value) return logs.value
  return logs.value.filter(log => log.level === logLevel.value)
})

const logStats = computed(() => {
  const stats = { info: 0, warning: 0, error: 0 }
  logs.value.forEach(log => {
    stats[log.level]++
  })
  return stats
})

// 方法
const handleClose = () => {
  visible.value = false
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    running: 'primary',
    paused: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatTime = (timestamp: number | string) => {
  const date = typeof timestamp === 'number' 
    ? (timestamp > 9999999999 ? new Date(timestamp) : new Date(timestamp * 1000))
    : new Date(timestamp)
  return date.toLocaleString()
}

const refreshLogs = async () => {
  if (!props.task) return
  
  try {
    refreshing.value = true
    // 这里应该调用API获取任务日志
    // const response = await crawlerApi.getTaskLogs(props.task.task_id)
    // logs.value = response.logs
    
    // 模拟日志数据
    const mockLogs: LogEntry[] = [
      {
        timestamp: Date.now() - 300000,
        level: 'info',
        message: '任务开始执行'
      },
      {
        timestamp: Date.now() - 250000,
        level: 'info',
        message: '开始爬取大学数据...'
      },
      {
        timestamp: Date.now() - 200000,
        level: 'info',
        message: '成功获取 50 条大学记录'
      },
      {
        timestamp: Date.now() - 150000,
        level: 'warning',
        message: '部分请求响应较慢，正在重试...'
      },
      {
        timestamp: Date.now() - 100000,
        level: 'info',
        message: '数据处理完成，共处理 100 条记录'
      },
      {
        timestamp: Date.now() - 50000,
        level: 'error',
        message: '网络连接超时，正在重新连接...'
      },
      {
        timestamp: Date.now(),
        level: 'info',
        message: '任务执行完成'
      }
    ]
    
    logs.value = mockLogs
    
    if (autoScroll.value) {
      await nextTick()
      scrollToBottom()
    }
    
  } catch (error) {
    console.error('刷新日志失败:', error)
    ElMessage.error('刷新日志失败')
  } finally {
    refreshing.value = false
  }
}

const clearLogs = () => {
  logs.value = []
  ElMessage.success('日志已清空')
}

const downloadLogs = () => {
  if (logs.value.length === 0) {
    ElMessage.warning('暂无日志可下载')
    return
  }
  
  const logText = logs.value
    .map(log => `[${formatTime(log.timestamp)}] [${log.level.toUpperCase()}] ${log.message}`)
    .join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `task-${props.task?.task_id}-logs-${new Date().toISOString().slice(0, 19)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('日志已下载')
}

const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight
  }
}

// 监听任务变化
watch(() => props.task, (newTask) => {
  if (newTask && visible.value) {
    refreshLogs()
  }
})

// 监听对话框显示
watch(visible, (isVisible) => {
  if (isVisible && props.task) {
    refreshLogs()
  }
})

// 自动刷新日志
let refreshTimer: number | null = null

onMounted(() => {
  if (visible.value && props.task) {
    refreshLogs()
  }
  
  // 每10秒自动刷新一次日志
  refreshTimer = window.setInterval(() => {
    if (visible.value && props.task && ['running', 'paused'].includes(props.task.status)) {
      refreshLogs()
    }
  }, 10000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.task-log-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.log-container {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.log-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #e4e7ed;
}

.task-info {
  font-size: 12px;
  color: #606266;
}

.log-content {
  flex: 1;
  overflow-y: auto;
  background: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  padding: 12px;
}

.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #ffffff;
  color: #909399;
}

.log-lines {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.log-line {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.log-line:hover {
  background: rgba(255, 255, 255, 0.05);
}

.log-line.log-info {
  border-left: 3px solid #409eff;
}

.log-line.log-warning {
  border-left: 3px solid #e6a23c;
  background: rgba(230, 162, 60, 0.1);
}

.log-line.log-error {
  border-left: 3px solid #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}

.log-time {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
  min-width: 140px;
}

.log-level {
  font-weight: bold;
  min-width: 60px;
  text-align: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.log-line.log-info .log-level {
  background: #409eff;
  color: white;
}

.log-line.log-warning .log-level {
  background: #e6a23c;
  color: white;
}

.log-line.log-error .log-level {
  background: #f56c6c;
  color: white;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

.log-stats {
  padding: 8px 16px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  border-radius: 0 0 8px 8px;
  font-size: 12px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
