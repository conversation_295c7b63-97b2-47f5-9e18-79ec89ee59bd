<template>
  <div class="crawler-task-manager">
    <div class="manager-header">
      <div class="header-title">
        <h3>爬虫任务管理</h3>
        <el-tag type="info" size="small">
          活跃任务: {{ activeTasks.length }}
        </el-tag>
      </div>
      
      <div class="header-actions">
        <el-button-group>
          <el-button @click="refreshTasks" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="showCreateDialog = true" type="primary">
            <el-icon><Plus /></el-icon>
            新建任务
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <!-- 任务统计 -->
    <div class="task-stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <el-statistic title="总任务数" :value="taskStats.total" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <el-statistic title="运行中" :value="taskStats.running" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <el-statistic title="已完成" :value="taskStats.completed" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <el-statistic title="失败" :value="taskStats.failed" />
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 活跃任务 -->
    <div v-if="activeTasks.length > 0" class="active-tasks">
      <h4>活跃任务</h4>
      <div class="task-grid">
        <div v-for="task in activeTasks" :key="task.task_id" class="task-card">
          <TaskProgress :task-id="task.task_id" />
        </div>
      </div>
    </div>
    
    <!-- 任务历史 -->
    <div class="task-history">
      <div class="history-header">
        <h4>任务历史</h4>
        <div class="history-filters">
          <el-space>
            <el-select v-model="filterType" placeholder="数据类型" size="small" style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="大学数据" value="university" />
              <el-option label="专业数据" value="major" />
            </el-select>
            
            <el-select v-model="filterStatus" placeholder="状态" size="small" style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="运行中" value="running" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-space>
        </div>
      </div>
      
      <el-table 
        :data="filteredTasks" 
        v-loading="loading"
        stripe
        @row-click="selectTask"
      >
        <el-table-column prop="task_name" label="任务名称" min-width="150" />
        <el-table-column prop="data_type" label="数据类型" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="row.data_type === 'university' ? 'primary' : 'success'">
              {{ row.data_type === 'university' ? '大学' : '专业' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始时间" width="160">
          <template #default="{ row }">
            {{ row.start_time ? formatTime(row.start_time) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="耗时" width="100">
          <template #default="{ row }">
            {{ row.duration ? formatDuration(row.duration * 1000) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="total_records" label="记录数" width="100" />
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button @click.stop="viewTaskDetail(row)" title="查看详情">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button @click.stop="showTaskLogs(row)" title="查看日志">
                <el-icon><Document /></el-icon>
              </el-button>
              <el-button
                v-if="['running', 'paused'].includes(row.status)"
                @click.stop="showTaskProgress(row)"
                type="primary"
                title="监控进度"
              >
                <el-icon><Monitor /></el-icon>
              </el-button>

              <!-- 任务控制按钮 -->
              <el-button
                v-if="row.status === 'running'"
                @click.stop="pauseTask(row)"
                type="warning"
                title="暂停任务"
              >
                <el-icon><VideoPause /></el-icon>
              </el-button>
              <el-button
                v-if="row.status === 'paused'"
                @click.stop="resumeTask(row)"
                type="success"
                title="恢复任务"
              >
                <el-icon><VideoPlay /></el-icon>
              </el-button>
              <el-button
                v-if="['running', 'paused'].includes(row.status)"
                @click.stop="cancelTask(row)"
                type="danger"
                title="取消任务"
              >
                <el-icon><Close /></el-icon>
              </el-button>

              <el-button
                v-if="['completed', 'failed', 'cancelled'].includes(row.status)"
                @click.stop="restartTask(row)"
                type="success"
                title="重新启动"
              >
                <el-icon><Refresh /></el-icon>
              </el-button>
              <el-button @click.stop="deleteTask(row)" type="danger" title="删除任务">
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalTasks"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadTasks"
          @current-change="loadTasks"
        />
      </div>
    </div>
    
    <!-- 创建任务对话框 -->
    <CreateTaskDialog
      v-model="showCreateDialog"
      @task-created="handleTaskCreated"
    />

    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model="showDetailDialog"
      :task="selectedTask"
      @open-progress="showTaskProgress"
    />

    <!-- 任务进度对话框 -->
    <TaskProgressDialog
      v-model="showProgressDialog"
      :task="selectedTask"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import {
  Refresh,
  Plus,
  View,
  Monitor,
  Delete,
  VideoPause,
  VideoPlay,
  Close,
  Document
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useCrawlerStore } from '../stores/crawler'
import TaskProgress from './TaskProgress.vue'
import CreateTaskDialog from './CreateTaskDialog.vue'
import TaskDetailDialog from './TaskDetailDialog.vue'
import TaskProgressDialog from './TaskProgressDialog.vue'
import type { TaskInfo } from '../stores/crawler'

const crawlerStore = useCrawlerStore()

// 定义emit
const emit = defineEmits<{
  showLogs: [task: TaskInfo]
}>()

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showProgressDialog = ref(false)
const selectedTask = ref<TaskInfo | null>(null)

// 分页和过滤
const currentPage = ref(1)
const pageSize = ref(20)
const totalTasks = ref(0)
const filterType = ref('')
const filterStatus = ref('')

// 定时器
let refreshTimer: number | null = null

// 计算属性
const activeTasks = computed(() => crawlerStore.getActiveTasks)
const allTasks = computed(() => crawlerStore.getAllTasks)
const taskStats = computed(() => crawlerStore.getTaskStats)

const filteredTasks = computed(() => {
  let tasks = allTasks.value
  
  if (filterType.value) {
    tasks = tasks.filter(task => task.data_type === filterType.value)
  }
  
  if (filterStatus.value) {
    tasks = tasks.filter(task => task.status === filterStatus.value)
  }
  
  return tasks
})

// 方法
const loadTasks = async () => {
  try {
    loading.value = true
    const response = await crawlerStore.loadTasks({
      page: currentPage.value,
      size: pageSize.value,
      data_type: filterType.value || undefined,
      status: filterStatus.value || undefined
    })
    totalTasks.value = response.total
  } catch (error) {
    console.error('加载任务列表失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshTasks = async () => {
  await loadTasks()
  ElMessage.success('任务列表已刷新')
}

const selectTask = (task: TaskInfo) => {
  selectedTask.value = task
}

const viewTaskDetail = (task: TaskInfo) => {
  selectedTask.value = task
  showDetailDialog.value = true
}

const showTaskProgress = (task: TaskInfo) => {
  selectedTask.value = task
  showProgressDialog.value = true
}

const showTaskLogs = (task: TaskInfo) => {
  emit('showLogs', task)
}

const pauseTask = async (task: TaskInfo) => {
  try {
    await crawlerStore.pauseTask(task.task_id)
    await loadTasks()
  } catch (error) {
    console.error('暂停任务失败:', error)
    ElMessage.error(`暂停任务失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

const resumeTask = async (task: TaskInfo) => {
  try {
    await crawlerStore.resumeTask(task.task_id)
    await loadTasks()
  } catch (error) {
    console.error('恢复任务失败:', error)
    ElMessage.error(`恢复任务失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

const cancelTask = async (task: TaskInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 "${task.task_name}" 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await crawlerStore.cancelTask(task.task_id)
    await loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消任务失败:', error)
      ElMessage.error(`取消任务失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
}

const deleteTask = async (task: TaskInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${task.task_name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await crawlerStore.deleteTask(task.task_id)
    await loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error)
      ElMessage.error(`删除任务失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
}

const handleTaskCreated = (response: any) => {
  // 使用后端返回的message，而不是尝试访问不存在的task_name
  ElMessage.success(response.message || '任务已创建')
  loadTasks()
}

const restartTask = async (task: TaskInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要重新启动任务 "${task.task_name}" 吗？`,
      '确认重启',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await crawlerStore.restartTask(task.task_id)
    await loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重启任务失败:', error)
    }
  }
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    running: 'primary',
    paused: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString()
}

const formatDuration = (ms: number) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

// 监听过滤条件变化
watch([filterType, filterStatus], () => {
  currentPage.value = 1
  loadTasks()
})

// 组件挂载时加载数据
onMounted(async () => {
  await crawlerStore.connectWebSocket()
  await loadTasks()

  // 启动定时器自动刷新任务列表（每30秒）
  refreshTimer = window.setInterval(async () => {
    try {
      await loadTasks()
    } catch (error) {
      console.error('自动刷新任务列表失败:', error)
    }
  }, 30000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

// 暴露方法给父组件
defineExpose({
  showCreateDialog,
  loadTasks,
  refreshTasks
})
</script>

<style scoped>
.crawler-task-manager {
  padding: 0;
  background: #f8fafc;
  min-height: 100vh;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px 32px;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  letter-spacing: -0.025em;
}

.header-actions .el-button-group {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.header-actions .el-button {
  border: 1px solid #e2e8f0;
  background: #ffffff;
  color: #475569;
  font-weight: 500;
  padding: 10px 16px;
  transition: all 0.2s ease;
}

.header-actions .el-button:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #334155;
}

.header-actions .el-button--primary {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.header-actions .el-button--primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.task-stats {
  margin-bottom: 24px;
  padding: 0 32px;
}

.stat-card {
  text-align: center;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.active-tasks {
  margin-bottom: 24px;
  padding: 0 32px;
}

.active-tasks h4 {
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.task-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

.task-history h4 {
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  padding: 0 32px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px 32px;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
}

.el-table {
  margin: 0 32px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e2e8f0;
}

.el-table :deep(.el-table__header) {
  background: #f8fafc;
}

.el-table :deep(.el-table__header th) {
  background: transparent;
  color: #475569;
  font-weight: 600;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
  padding: 16px 12px;
}

.el-table :deep(.el-table__row) {
  transition: all 0.2s ease;
}

.el-table :deep(.el-table__row:hover) {
  background: #f8fafc;
}

.el-table :deep(.el-table__row td) {
  padding: 16px 12px;
  border-bottom: 1px solid #f1f5f9;
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  padding: 24px 32px;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
}

/* 按钮组样式优化 */
.el-button-group .el-button {
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
  background: #ffffff;
  color: #475569;
  font-size: 12px;
  padding: 6px 12px;
  margin: 0;
}

.el-button-group .el-button:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #334155;
}

.el-button-group .el-button--warning {
  background: #fbbf24;
  border-color: #fbbf24;
  color: #ffffff;
}

.el-button-group .el-button--warning:hover {
  background: #f59e0b;
  border-color: #f59e0b;
}

.el-button-group .el-button--success {
  background: #10b981;
  border-color: #10b981;
  color: #ffffff;
}

.el-button-group .el-button--success:hover {
  background: #059669;
  border-color: #059669;
}

.el-button-group .el-button--danger {
  background: #ef4444;
  border-color: #ef4444;
  color: #ffffff;
}

.el-button-group .el-button--danger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

/* 状态标签样式优化 */
.el-tag {
  border-radius: 6px;
  font-weight: 500;
  padding: 4px 8px;
  font-size: 12px;
  border: none;
}

.el-tag--success {
  background: #dcfce7;
  color: #166534;
}

.el-tag--warning {
  background: #fef3c7;
  color: #92400e;
}

.el-tag--danger {
  background: #fee2e2;
  color: #991b1b;
}

.el-tag--info {
  background: #e0f2fe;
  color: #0c4a6e;
}

.el-tag--primary {
  background: #dbeafe;
  color: #1e40af;
}
</style>
