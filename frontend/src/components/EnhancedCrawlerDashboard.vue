<template>
  <div class="enhanced-crawler-dashboard">
    <!-- 顶部导航栏 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="dashboard-title">
            <el-icon class="title-icon"><DataAnalysis /></el-icon>
            高考数据爬虫管理平台
          </h1>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>首页</el-breadcrumb-item>
            <el-breadcrumb-item>爬虫管理</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-space>
            <el-badge :value="systemStatus.activeTasks" :max="99" type="primary">
              <el-button circle>
                <el-icon><Bell /></el-icon>
              </el-button>
            </el-badge>
            <el-dropdown>
              <el-button circle>
                <el-icon><Setting /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="showConfigEditor = true">
                    <el-icon><Tools /></el-icon>
                    爬虫配置
                  </el-dropdown-item>
                  <el-dropdown-item @click="showScheduleManager = true">
                    <el-icon><Clock /></el-icon>
                    调度管理
                  </el-dropdown-item>
                  <el-dropdown-item @click="showHealthMonitor = true">
                    <el-icon><Monitor /></el-icon>
                    系统监控
                  </el-dropdown-item>
                  <el-dropdown-item @click="showExportManager = true">
                    <el-icon><Download /></el-icon>
                    数据导出
                  </el-dropdown-item>
                  <el-dropdown-item @click="showUserPreferences = true">
                    <el-icon><User /></el-icon>
                    用户设置
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="refreshAll">
                    <el-icon><Refresh /></el-icon>
                    刷新数据
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-space>
        </div>
      </div>
    </div>

    <!-- 系统状态卡片 -->
    <div class="status-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="status-card primary">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ systemStatus.activeTasks }}</div>
                <div class="card-label">活跃任务</div>
              </div>
            </div>
            <div class="card-trend">
              <el-icon class="trend-icon up"><CaretTop /></el-icon>
              <span class="trend-text">较昨日 +12%</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="status-card success">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ systemStatus.completedToday }}</div>
                <div class="card-label">今日完成</div>
              </div>
            </div>
            <div class="card-trend">
              <el-icon class="trend-icon up"><CaretTop /></el-icon>
              <span class="trend-text">较昨日 +8%</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="status-card warning">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><DataLine /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ formatNumber(systemStatus.totalRecords) }}</div>
                <div class="card-label">总记录数</div>
              </div>
            </div>
            <div class="card-trend">
              <el-icon class="trend-icon up"><CaretTop /></el-icon>
              <span class="trend-text">较昨日 +156</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="status-card info">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ systemStatus.avgDuration }}s</div>
                <div class="card-label">平均耗时</div>
              </div>
            </div>
            <div class="card-trend">
              <el-icon class="trend-icon down"><CaretBottom /></el-icon>
              <span class="trend-text">较昨日 -5%</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-content">
      <el-row>
        <!-- 任务管理（全宽） -->
        <el-col :span="24">
          <el-card shadow="never" class="main-card">
            <template #header>
              <div class="card-header">
                <h3>任务管理</h3>
                <el-space>
                  <el-button @click="showCreateDialog = true" type="primary" size="small">
                    <el-icon><Plus /></el-icon>
                    新建任务
                  </el-button>
                  <el-button @click="refreshTasks" size="small">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                </el-space>
              </div>
            </template>

            <!-- 任务筛选 -->
            <div class="task-filters">
              <el-space wrap>
                <el-select v-model="taskFilter.type" placeholder="任务类型" size="small" style="width: 120px">
                  <el-option label="全部" value="" />
                  <el-option label="大学数据" value="university" />
                  <el-option label="专业数据" value="major" />
                </el-select>
                <el-select v-model="taskFilter.status" placeholder="状态" size="small" style="width: 120px">
                  <el-option label="全部" value="" />
                  <el-option label="运行中" value="running" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="失败" value="failed" />
                </el-select>
                <el-date-picker
                  v-model="taskFilter.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                  style="width: 240px"
                />
              </el-space>
            </div>

            <!-- 任务列表 -->
            <CrawlerTaskManager ref="taskManagerRef" @show-logs="handleShowLogs" />
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 对话框和组件 -->
    <CreateTaskDialog v-model="showCreateDialog" @task-created="handleTaskCreated" />

    <!-- 任务日志对话框 -->
    <TaskLogDialog
      v-model="showLogDialog"
      :task="selectedTask"
    />

    <!-- 功能组件对话框 -->
    <el-dialog v-model="showConfigEditor" title="爬虫配置管理" width="90%" top="5vh">
      <CrawlerConfigEditor />
    </el-dialog>

    <el-dialog v-model="showScheduleManager" title="调度管理" width="90%" top="5vh">
      <CrawlerScheduleManager />
    </el-dialog>

    <el-dialog v-model="showHealthMonitor" title="系统健康监控" width="90%" top="5vh">
      <SystemHealthMonitor />
    </el-dialog>

    <el-dialog v-model="showExportManager" title="数据导出管理" width="90%" top="5vh">
      <DataExportManager />
    </el-dialog>

    <el-dialog v-model="showUserPreferences" title="用户设置" width="80%" top="5vh">
      <UserPreferences />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import {
  DataAnalysis,
  Bell,
  Setting,
  Tools,
  Clock,
  Refresh,
  Monitor,
  SuccessFilled,
  DataLine,
  Timer,
  CaretTop,
  CaretBottom,
  Plus,
  Download,
  User
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useCrawlerStore } from '../stores/crawler'
import CrawlerTaskManager from './CrawlerTaskManager.vue'
import SystemStatus from './SystemStatus.vue'
import CreateTaskDialog from './CreateTaskDialog.vue'
import TaskLogDialog from './TaskLogDialog.vue'
import CrawlerConfigEditor from './CrawlerConfigEditor.vue'
import CrawlerScheduleManager from './CrawlerScheduleManager.vue'
import SystemHealthMonitor from './SystemHealthMonitor.vue'
import DataExportManager from './DataExportManager.vue'
import UserPreferences from './UserPreferences.vue'

const crawlerStore = useCrawlerStore()

// 响应式数据
const showCreateDialog = ref(false)
const showLogDialog = ref(false)
const showConfigEditor = ref(false)
const showScheduleManager = ref(false)
const showHealthMonitor = ref(false)
const showExportManager = ref(false)
const showUserPreferences = ref(false)
const autoRefresh = ref(true)
const taskManagerRef = ref()
const selectedTask = ref<any>(null)

// 系统状态
const systemStatus = reactive({
  activeTasks: 0,
  completedToday: 0,
  totalRecords: 0,
  avgDuration: 0
})

// 任务筛选
const taskFilter = reactive({
  type: '',
  status: '',
  dateRange: null as any
})

// 定时器
let refreshTimer: number | null = null

// 计算属性
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toLocaleString()
}

// 方法
const refreshAll = async () => {
  try {
    await Promise.all([
      loadSystemStatus(),
      refreshTasks()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  }
}

const loadSystemStatus = async () => {
  try {
    // 这里调用API获取系统状态
    // const response = await crawlerApi.getSystemStatus()
    
    // 模拟数据
    systemStatus.activeTasks = 3
    systemStatus.completedToday = 15
    systemStatus.totalRecords = 125680
    systemStatus.avgDuration = 45
  } catch (error) {
    console.error('加载系统状态失败:', error)
  }
}

const refreshTasks = async () => {
  if (taskManagerRef.value) {
    await taskManagerRef.value.refreshTasks()
  }
}

const handleTaskCreated = (response: any) => {
  ElMessage.success(response.message || '任务创建成功')
  refreshTasks()
  loadSystemStatus()
}

const handleShowLogs = (task: any) => {
  selectedTask.value = task
  showLogDialog.value = true
}

// 组件挂载时初始化
onMounted(async () => {
  await crawlerStore.connectWebSocket()
  await loadSystemStatus()
  
  // 启动自动刷新
  if (autoRefresh.value) {
    refreshTimer = window.setInterval(async () => {
      if (autoRefresh.value) {
        await loadSystemStatus()
      }
    }, 30000) // 每30秒刷新一次
  }
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

// 暴露方法给父组件
defineExpose({
  refreshAll,
  showCreateDialog,
  showConfigEditor,
  showScheduleManager,
  showHealthMonitor,
  showExportManager,
  showUserPreferences
})
</script>

<style scoped>
.enhanced-crawler-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 16px 24px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dashboard-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.title-icon {
  font-size: 28px;
  color: #667eea;
}

.status-cards {
  max-width: 1400px;
  margin: 0 auto 24px;
  padding: 0 24px;
}

.status-card {
  border-radius: 12px;
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.status-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.status-card.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.status-card.warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.status-card.info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
}

.card-icon {
  font-size: 32px;
  opacity: 0.8;
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  opacity: 0.9;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  margin-top: 8px;
  opacity: 0.8;
}

.trend-icon.up {
  color: #52c41a;
}

.trend-icon.down {
  color: #ff4d4f;
}

.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.main-card {
  border-radius: 12px;
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.task-filters {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}
</style>
