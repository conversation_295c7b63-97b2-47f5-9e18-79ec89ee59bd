/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    BaseDataManager: typeof import('./src/components/BaseDataManager.vue')['default']
    CrawlerConfigEditor: typeof import('./src/components/CrawlerConfigEditor.vue')['default']
    CrawlerScheduleManager: typeof import('./src/components/CrawlerScheduleManager.vue')['default']
    CrawlerTaskManager: typeof import('./src/components/CrawlerTaskManager.vue')['default']
    CreateTaskDialog: typeof import('./src/components/CreateTaskDialog.vue')['default']
    DataExportManager: typeof import('./src/components/DataExportManager.vue')['default']
    DataOverview: typeof import('./src/components/DataOverview.vue')['default']
    DataVisualization: typeof import('./src/components/DataVisualization.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElStatisticCountdown: typeof import('element-plus/es')['ElStatisticCountdown']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    EnhancedCrawlerDashboard: typeof import('./src/components/EnhancedCrawlerDashboard.vue')['default']
    ModernCrawlerDashboard: typeof import('./src/components/ModernCrawlerDashboard.vue')['default']
    RealTimeLog: typeof import('./src/components/RealTimeLog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SystemHealthMonitor: typeof import('./src/components/SystemHealthMonitor.vue')['default']
    SystemStatus: typeof import('./src/components/SystemStatus.vue')['default']
    TaskDetailDialog: typeof import('./src/components/TaskDetailDialog.vue')['default']
    TaskLogDialog: typeof import('./src/components/TaskLogDialog.vue')['default']
    TaskProgress: typeof import('./src/components/TaskProgress.vue')['default']
    TaskProgressDialog: typeof import('./src/components/TaskProgressDialog.vue')['default']
    UserPreferences: typeof import('./src/components/UserPreferences.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
