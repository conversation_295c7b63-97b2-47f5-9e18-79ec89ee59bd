#!/usr/bin/env python3
"""
最终功能验证测试
"""

import asyncio
import aiohttp
import json
from datetime import datetime

API_BASE = "http://localhost:8000"

async def test_all_functionality():
    """测试所有功能"""
    print("🎯 高考数据平台最终功能验证")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        # 1. 基础API测试
        print("\n1. 基础API功能验证...")
        
        # 健康检查
        try:
            async with session.get(f"{API_BASE}/api/health") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 健康检查: {data['status']}")
                else:
                    print(f"❌ 健康检查失败: {resp.status}")
                    return
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return
        
        # 系统信息
        try:
            async with session.get(f"{API_BASE}/api/info") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 系统信息: 版本 {data['system']['version']}")
                else:
                    print(f"❌ 系统信息获取失败: {resp.status}")
        except Exception as e:
            print(f"❌ 系统信息异常: {e}")
        
        # 2. 爬虫配置测试
        print("\n2. 爬虫配置验证...")
        try:
            async with session.get(f"{API_BASE}/api/crawler/config") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 爬虫配置获取成功")
                    print(f"   - 目标年份: {data['data']['target_years']}")
                    print(f"   - 省份数量: {len(data['data']['provinces'])}")
                    print(f"   - 科类配置: {list(data['data']['science_types'].keys())}")
                    print(f"   - 批次配置: {list(data['data']['batch_types'].keys())}")
                else:
                    print(f"❌ 爬虫配置获取失败: {resp.status}")
        except Exception as e:
            print(f"❌ 爬虫配置异常: {e}")
        
        # 3. 任务管理测试
        print("\n3. 任务管理验证...")
        try:
            async with session.get(f"{API_BASE}/api/crawler/tasks") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 任务列表获取成功")
                    print(f"   - 总任务数: {data['total']}")
                    print(f"   - 当前页任务: {len(data['data'])}")
                    
                    # 显示任务状态统计
                    status_count = {}
                    for task in data['data']:
                        status = task['status']
                        status_count[status] = status_count.get(status, 0) + 1
                    
                    print(f"   - 任务状态统计: {status_count}")
                else:
                    print(f"❌ 任务列表获取失败: {resp.status}")
        except Exception as e:
            print(f"❌ 任务列表异常: {e}")
        
        # 4. 数据分析API测试
        print("\n4. 数据分析API验证...")
        
        # 概览统计
        try:
            async with session.get(f"{API_BASE}/api/analytics/overview") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 数据概览获取成功")
                    if data.get('success'):
                        stats = data['data']['basic_stats']
                        print(f"   - 大学数量: {stats['total_universities']}")
                        print(f"   - 专业数量: {stats['total_majors']}")
                        print(f"   - 录取记录: {stats['total_admission_records']}")
                        print(f"   - 专业记录: {stats['total_major_records']}")
                    else:
                        print(f"   - 错误: {data.get('error', {}).get('message', 'Unknown')}")
                else:
                    print(f"❌ 数据概览获取失败: {resp.status}")
        except Exception as e:
            print(f"❌ 数据概览异常: {e}")
        
        # 趋势分析
        try:
            async with session.get(f"{API_BASE}/api/analytics/trends") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 趋势分析获取成功")
                    if data.get('success'):
                        trends = data['data']
                        print(f"   - 趋势数据: {len(trends.get('trends', []))} 个数据点")
                    else:
                        print(f"   - 错误: {data.get('error', {}).get('message', 'Unknown')}")
                else:
                    print(f"❌ 趋势分析获取失败: {resp.status}")
        except Exception as e:
            print(f"❌ 趋势分析异常: {e}")
        
        # 5. WebSocket连接测试
        print("\n5. WebSocket连接验证...")
        try:
            import websockets
            
            async with websockets.connect(f"ws://localhost:8000/api/ws/crawler") as websocket:
                print("✅ WebSocket连接成功")
                
                # 等待接收消息
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    data = json.loads(message)
                    print(f"✅ 收到WebSocket消息: {data.get('type', 'unknown')}")
                except asyncio.TimeoutError:
                    print("⚠️ WebSocket连接正常，但3秒内未收到消息")
                    
        except ImportError:
            print("⚠️ websockets库未安装，跳过WebSocket测试")
        except Exception as e:
            print(f"❌ WebSocket连接异常: {e}")
        
        # 6. 创建测试任务
        print("\n6. 任务创建验证...")
        task_data = {
            "years": [2024],
            "provinces": ["北京"],
            "science_types": ["1"],
            "batch_types": ["2"]
        }
        
        try:
            headers = {"Content-Type": "application/json"}
            async with session.post(
                f"{API_BASE}/api/crawler/universities/start",
                json=task_data,
                headers=headers
            ) as resp:
                response_text = await resp.text()
                
                if resp.status == 200:
                    data = json.loads(response_text) if response_text else {}
                    print(f"✅ 任务创建成功!")
                    print(f"   - 任务ID: {data.get('task_id', 'N/A')}")
                    print(f"   - 消息: {data.get('message', 'N/A')}")
                    
                    # 等待一下，然后检查任务状态
                    task_id = data.get('task_id')
                    if task_id:
                        await asyncio.sleep(3)
                        await check_task_status(session, task_id)
                        
                else:
                    print(f"❌ 任务创建失败: {resp.status}")
                    print(f"   响应: {response_text}")
                    
        except Exception as e:
            print(f"❌ 任务创建异常: {e}")

async def check_task_status(session, task_id):
    """检查任务状态"""
    print(f"\n📊 检查任务状态: {task_id}")
    try:
        async with session.get(f"{API_BASE}/api/crawler/tasks/{task_id}") as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✅ 任务状态查询成功")
                print(f"   - 状态: {data.get('status', 'N/A')}")
                print(f"   - 开始时间: {data.get('start_time', 'N/A')}")
                print(f"   - 总请求数: {data.get('total_requests', 0)}")
                print(f"   - 成功请求: {data.get('success_requests', 0)}")
                print(f"   - 总记录数: {data.get('total_records', 0)}")
            else:
                response_text = await resp.text()
                print(f"❌ 任务状态查询失败: {resp.status}")
                print(f"   响应: {response_text}")
    except Exception as e:
        print(f"❌ 任务状态查询异常: {e}")

async def main():
    """主函数"""
    print("🎯 高考数据平台最终功能验证")
    print("=" * 60)
    
    await test_all_functionality()
    
    print("\n" + "=" * 60)
    print("✅ 最终功能验证完成!")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
