<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高考数据平台 - 全面功能验证</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 32px;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .header p {
            color: #64748b;
            font-size: 16px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }
        
        .test-section h3 {
            color: #1e293b;
            margin-bottom: 16px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-item {
            margin-bottom: 12px;
            padding: 12px;
            border-radius: 8px;
            background: #f8fafc;
            border-left: 4px solid #cbd5e1;
        }
        
        .test-item.success {
            background: #f0fdf4;
            border-left-color: #22c55e;
        }
        
        .test-item.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        
        .test-item.loading {
            background: #fefce8;
            border-left-color: #eab308;
        }
        
        .test-name {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .test-result {
            font-size: 14px;
            color: #64748b;
        }
        
        .test-details {
            font-size: 12px;
            color: #94a3b8;
            margin-top: 4px;
            font-family: 'Courier New', monospace;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s;
            margin: 8px 8px 8px 0;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }
        
        .btn:disabled {
            background: #cbd5e1;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn.success {
            background: #22c55e;
        }
        
        .btn.danger {
            background: #ef4444;
        }
        
        .btn.warning {
            background: #eab308;
        }
        
        .status-bar {
            background: #1e293b;
            color: white;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-item {
            text-align: center;
        }
        
        .status-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .status-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .log-container {
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 16px;
        }
        
        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
        }
        
        .log-entry.info {
            color: #60a5fa;
        }
        
        .log-entry.success {
            color: #34d399;
        }
        
        .log-entry.error {
            color: #f87171;
        }
        
        .log-entry.warning {
            color: #fbbf24;
        }
        
        .icon {
            width: 16px;
            height: 16px;
            display: inline-block;
        }
        
        .icon.success {
            color: #22c55e;
        }
        
        .icon.error {
            color: #ef4444;
        }
        
        .icon.loading {
            color: #eab308;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 高考数据平台全面功能验证</h1>
            <p>完整的前后端功能测试和API接口验证</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <div class="status-value" id="totalTests">0</div>
                <div class="status-label">总测试数</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="passedTests">0</div>
                <div class="status-label">通过</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="failedTests">0</div>
                <div class="status-label">失败</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="testProgress">0%</div>
                <div class="status-label">进度</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-bottom: 32px;">
            <button class="btn" onclick="runAllTests()">🔍 开始全面验证</button>
            <button class="btn success" onclick="runBasicTests()">⚡ 基础功能测试</button>
            <button class="btn warning" onclick="runAdvancedTests()">🎯 高级功能测试</button>
            <button class="btn danger" onclick="clearResults()">🗑️ 清除结果</button>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
        
        <div class="test-grid">
            <div class="test-section">
                <h3>🔧 基础API测试</h3>
                <div id="basicApiTests"></div>
            </div>
            
            <div class="test-section">
                <h3>🕷️ 爬虫功能测试</h3>
                <div id="crawlerTests"></div>
            </div>
            
            <div class="test-section">
                <h3>📊 数据分析测试</h3>
                <div id="analyticsTests"></div>
            </div>
            
            <div class="test-section">
                <h3>🔌 WebSocket测试</h3>
                <div id="websocketTests"></div>
            </div>
            
            <div class="test-section">
                <h3>🎛️ 任务管理测试</h3>
                <div id="taskTests"></div>
            </div>
            
            <div class="test-section">
                <h3>🛡️ 安全功能测试</h3>
                <div id="securityTests"></div>
            </div>
        </div>
        
        <div class="log-container" id="logContainer">
            <div class="log-entry info">[INFO] 等待开始测试...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        const WS_BASE = 'ws://localhost:8000';
        
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0,
            current: 0
        };
        
        let websocket = null;
        
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            
            const progress = testStats.total > 0 ? Math.round((testStats.current / testStats.total) * 100) : 0;
            document.getElementById('testProgress').textContent = progress + '%';
            document.getElementById('progressFill').style.width = progress + '%';
        }
        
        function addTestResult(sectionId, testName, success, details = '') {
            const section = document.getElementById(sectionId);
            const testItem = document.createElement('div');
            testItem.className = `test-item ${success ? 'success' : 'error'}`;
            
            testItem.innerHTML = `
                <div class="test-name">
                    <span class="icon ${success ? 'success' : 'error'}">${success ? '✅' : '❌'}</span>
                    ${testName}
                </div>
                <div class="test-result">${success ? '通过' : '失败'}</div>
                ${details ? `<div class="test-details">${details}</div>` : ''}
            `;
            
            section.appendChild(testItem);
            
            if (success) {
                testStats.passed++;
            } else {
                testStats.failed++;
            }
            testStats.current++;
            updateStats();
        }
        
        async function testApi(url, method = 'GET', data = null) {
            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(API_BASE + url, options);
                const result = await response.json();
                
                return {
                    success: response.ok,
                    data: result,
                    status: response.status
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        async function runBasicApiTests() {
            log('开始基础API测试...', 'info');
            
            // 健康检查
            const health = await testApi('/api/health');
            addTestResult('basicApiTests', '健康检查API', health.success, 
                health.success ? `状态: ${health.data.status}` : health.error);
            
            // 系统信息
            const info = await testApi('/api/info');
            addTestResult('basicApiTests', '系统信息API', info.success,
                info.success ? `版本: ${info.data.system.version}` : info.error);
            
            // 大学列表
            const universities = await testApi('/api/universities/');
            addTestResult('basicApiTests', '大学列表API', universities.success,
                universities.success ? `返回 ${universities.data.length || 0} 条记录` : universities.error);
            
            // 专业列表
            const majors = await testApi('/api/majors/');
            addTestResult('basicApiTests', '专业列表API', majors.success,
                majors.success ? `返回 ${majors.data.length || 0} 条记录` : majors.error);
            
            // 统计信息
            const stats = await testApi('/api/statistics/overview');
            addTestResult('basicApiTests', '统计信息API', stats.success,
                stats.success ? '统计数据正常' : stats.error);
        }
        
        async function runCrawlerTests() {
            log('开始爬虫功能测试...', 'info');
            
            // 爬虫配置
            const config = await testApi('/api/crawler/config');
            addTestResult('crawlerTests', '爬虫配置获取', config.success,
                config.success ? '配置加载成功' : config.error);
            
            // 任务列表
            const tasks = await testApi('/api/crawler/tasks');
            addTestResult('crawlerTests', '任务列表获取', tasks.success,
                tasks.success ? `当前任务数: ${tasks.data.length || 0}` : tasks.error);
            
            // 创建测试任务（小规模）
            const createTask = await testApi('/api/crawler/universities/start', 'POST', {
                years: [2024],
                provinces: ['北京'],
                science_types: ['1'],
                batch_types: ['2']
            });
            addTestResult('crawlerTests', '创建爬虫任务', createTask.success,
                createTask.success ? `任务ID: ${createTask.data.task_id}` : createTask.error);
        }
        
        async function runAnalyticsTests() {
            log('开始数据分析测试...', 'info');
            
            // 概览统计
            const overview = await testApi('/api/analytics/overview');
            addTestResult('analyticsTests', '数据概览', overview.success,
                overview.success ? '概览数据获取成功' : overview.error);
            
            // 趋势分析
            const trends = await testApi('/api/analytics/trends');
            addTestResult('analyticsTests', '趋势分析', trends.success,
                trends.success ? '趋势数据获取成功' : trends.error);
            
            // 排名分析
            const rankings = await testApi('/api/analytics/rankings');
            addTestResult('analyticsTests', '排名分析', rankings.success,
                rankings.success ? '排名数据获取成功' : rankings.error);
        }
        
        function runWebSocketTests() {
            log('开始WebSocket测试...', 'info');
            
            try {
                websocket = new WebSocket(`${WS_BASE}/api/ws/crawler`);
                
                websocket.onopen = () => {
                    addTestResult('websocketTests', 'WebSocket连接', true, '连接建立成功');
                    log('WebSocket连接成功', 'success');
                };
                
                websocket.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    addTestResult('websocketTests', '消息接收', true, `类型: ${data.type}`);
                    log(`收到WebSocket消息: ${data.type}`, 'info');
                };
                
                websocket.onerror = (error) => {
                    addTestResult('websocketTests', 'WebSocket连接', false, '连接失败');
                    log('WebSocket连接失败', 'error');
                };
                
                // 测试超时
                setTimeout(() => {
                    if (websocket.readyState !== WebSocket.OPEN) {
                        addTestResult('websocketTests', 'WebSocket连接', false, '连接超时');
                    }
                }, 5000);
                
            } catch (error) {
                addTestResult('websocketTests', 'WebSocket连接', false, error.message);
            }
        }
        
        async function runTaskTests() {
            log('开始任务管理测试...', 'info');
            
            // 获取任务列表
            const tasks = await testApi('/api/crawler/tasks');
            addTestResult('taskTests', '任务列表', tasks.success,
                tasks.success ? `任务数量: ${tasks.data.length || 0}` : tasks.error);
            
            if (tasks.success && tasks.data.length > 0) {
                const taskId = tasks.data[0].task_id;
                
                // 测试任务控制（如果有运行中的任务）
                const runningTask = tasks.data.find(t => t.status === 'running');
                if (runningTask) {
                    // 测试暂停
                    const pause = await testApi(`/api/ws/task/${runningTask.task_id}/action`, 'POST', {
                        action: 'pause'
                    });
                    addTestResult('taskTests', '任务暂停', pause.success, '暂停操作测试');
                }
            }
        }
        
        async function runSecurityTests() {
            log('开始安全功能测试...', 'info');
            
            // 测试限流（快速请求）
            const requests = [];
            for (let i = 0; i < 5; i++) {
                requests.push(testApi('/api/health'));
            }
            
            const results = await Promise.all(requests);
            const allSuccess = results.every(r => r.success);
            addTestResult('securityTests', '并发请求处理', allSuccess, '5个并发请求测试');
            
            // 测试无效路径
            const invalid = await testApi('/api/invalid-endpoint');
            addTestResult('securityTests', '无效路径处理', !invalid.success, 
                `状态码: ${invalid.status || 'N/A'}`);
        }
        
        async function runAdvancedTests() {
            testStats.total += 8; // 高级测试数量
            updateStats();
            
            await runAnalyticsTests();
            runWebSocketTests();
            await runTaskTests();
            await runSecurityTests();
        }
        
        async function runAllTests() {
            clearResults();
            log('开始全面功能验证...', 'info');
            
            // 计算总测试数
            testStats.total = 15; // 基础测试 + 高级测试
            updateStats();
            
            await runBasicApiTests();
            await runCrawlerTests();
            await runAdvancedTests();
            
            log(`测试完成! 通过: ${testStats.passed}, 失败: ${testStats.failed}`, 
                testStats.failed === 0 ? 'success' : 'warning');
        }
        
        function clearResults() {
            testStats = { total: 0, passed: 0, failed: 0, current: 0 };
            updateStats();
            
            ['basicApiTests', 'crawlerTests', 'analyticsTests', 'websocketTests', 'taskTests', 'securityTests']
                .forEach(id => {
                    document.getElementById(id).innerHTML = '';
                });
            
            document.getElementById('logContainer').innerHTML = 
                '<div class="log-entry info">[INFO] 结果已清除，等待新的测试...</div>';
            
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，准备开始测试', 'info');
            updateStats();
        });
    </script>
</body>
</html>
