# 高考数据收集分析平台 - 清洁环境配置
# 只包含新配置系统识别的变量

# ===========================================
# 应用基础配置
# ===========================================
APP_TITLE=高考数据收集分析平台
APP_DESCRIPTION=基于API文档设计的高考院校专业数据收集、存储、分析和可视化展示平台
APP_VERSION=0.2.0
APP_DEBUG=false
APP_HOST=0.0.0.0
APP_PORT=8000

# ===========================================
# 数据库配置
# ===========================================
DB_URL=sqlite:///E:\code\gaokao\backend\gaokao.db
DB_ECHO=false
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# ===========================================
# Redis配置
# ===========================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ===========================================
# 爬虫配置
# ===========================================
CRAWLER_API_TOKEN=1750037642981-1283-8OLZPNQ6Q86ROJT-1333330755
CRAWLER_BASE_URL=https://api.example.com
CRAWLER_REQUEST_DELAY=1.0
CRAWLER_MAX_RETRIES=3
CRAWLER_TIMEOUT=30
CRAWLER_CONCURRENT_LIMIT=5

# ===========================================
# 安全配置
# ===========================================
SECURITY_SECRET_KEY=your-super-secret-key-change-this-in-production
SECURITY_ALGORITHM=HS256
SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO
LOG_ROTATION=1 day
LOG_RETENTION=30 days
