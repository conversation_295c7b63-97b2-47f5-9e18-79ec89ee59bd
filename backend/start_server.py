#!/usr/bin/env python3
"""
启动服务器脚本
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

# 现在可以导入app模块
if __name__ == "__main__":
    import uvicorn
    from app.main import app
    
    print("🚀 启动高考数据收集分析平台...")
    print("📍 API文档: http://localhost:8000/docs")
    print("🔗 前端地址: http://localhost:3000")
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
