"""
高考数据爬虫核心类 - 基于API文档设计
"""

import asyncio
import httpx
import json
from typing import Dict, List, Optional, AsyncGenerator
from loguru import logger
from datetime import datetime

from .config import CrawlerConfig
from .data_processor import DataProcessor
from app.models.system import CrawlLog, CrawlStatus


class GaokaoSpider:
    """
    高考数据爬虫主类
    基于提供的API文档实现数据爬取
    """

    def __init__(self, config: Optional[CrawlerConfig] = None):
        self.config = config or CrawlerConfig.from_env()
        self.client: Optional[httpx.AsyncClient] = None
        self.data_processor = DataProcessor()
        self.session_stats = {
            "total_requests": 0,
            "success_requests": 0,
            "failed_requests": 0,
            "total_records": 0
        }

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def start(self):
        """启动爬虫客户端"""
        self.client = httpx.AsyncClient(
            timeout=self.config.TIMEOUT,
            headers=self.config.DEFAULT_HEADERS,
            limits=httpx.Limits(
                max_connections=self.config.MAX_CONCURRENT_REQUESTS,
                max_keepalive_connections=self.config.MAX_CONCURRENT_REQUESTS
            )
        )
        logger.info("爬虫客户端已启动")

    async def close(self):
        """关闭爬虫客户端"""
        if self.client:
            await self.client.aclose()
            logger.info("爬虫客户端已关闭")

    async def _make_request(self, url: str, data: Dict, retry_count: int = 0) -> Optional[Dict]:
        """
        发送HTTP请求
        
        Args:
            url: 请求URL
            data: 请求数据
            retry_count: 重试次数
            
        Returns:
            响应数据或None
        """
        try:
            self.session_stats["total_requests"] += 1

            response = await self.client.post(url, json=data)
            response.raise_for_status()

            result = response.json()

            # 检查API响应格式
            if not result.get("success"):
                logger.warning(f"API返回失败: {result.get('msg', '未知错误')}")
                return None

            self.session_stats["success_requests"] += 1
            return result

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP错误 {e.response.status_code}: {e}")
        except httpx.RequestError as e:
            logger.error(f"请求错误: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
        except Exception as e:
            logger.error(f"未知错误: {e}")

        self.session_stats["failed_requests"] += 1

        # 重试机制
        if retry_count < self.config.RETRY_TIMES:
            await asyncio.sleep(self.config.REQUEST_DELAY * (retry_count + 1))
            return await self._make_request(url, data, retry_count + 1)

        return None

    async def crawl_universities(self, year: int, province: str, science: str,
                                 batch: str = "1") -> AsyncGenerator[Dict, None]:
        """
        爬取大学数据
        
        Args:
            year: 年份
            province: 省份
            science: 科类
            batch: 批次
            
        Yields:
            大学数据字典
        """
        page_no = 1

        while True:
            # 构建请求参数
            params = self.config.get_university_request_params(
                year=year, province=province, science=science,
                batch=batch, page_no=page_no
            )
            logger.error(params)

            logger.info(f"爬取大学数据: {year}年 {province} {science} 第{page_no}页")

            # 发送请求
            result = await self._make_request(self.config.UNIVERSITY_API_URL, params)
            if not result:
                break

            # 解析数据
            data = result.get("data", {})
            universities = data.get("data", [])

            if not universities:
                break

            # 处理每条大学记录
            for university_data in universities:
                self.session_stats["total_records"] += 1
                yield university_data

            # 检查是否还有更多页
            total_pages = data.get("totle", 0)  # API中使用的是totle而不是total
            if page_no >= total_pages or len(universities) == 0:
                break

            page_no += 1
            await asyncio.sleep(self.config.REQUEST_DELAY)

    async def crawl_majors(self, year: int, province: str, science: str,
                           university_code: str, university_name: str = "", batch: str = "") -> List[Dict]:
        """
        爬取专业数据

        Args:
            year: 年份
            province: 省份
            science: 科类
            university_code: 大学代码
            university_name: 大学名称
            batch: 批次

        Returns:
            专业数据列表
        """
        # 构建请求参数
        params = self.config.get_major_request_params(
            year=year, province=province, science=science,
            collage=university_code, batch=batch, university_name=university_name
        )

        logger.info(f"爬取专业数据: {year}年 {province} {science} 学校{university_code}({university_name})")

        # 发送请求
        result = await self._make_request(self.config.MAJOR_API_URL, params)
        if not result:
            logger.warning(f"专业数据请求失败: {year}年 {province} {science} 学校{university_code}")
            return []

        # 解析数据
        majors = result.get("data", [])
        self.session_stats["total_records"] += len(majors)

        logger.info(f"获取到 {len(majors)} 条专业数据")
        return majors

    async def crawl_all_universities(self, years: List[int] = None,
                                     provinces: List[str] = None,
                                     science_types: List[str] = None) -> AsyncGenerator[Dict, None]:
        """
        爬取所有大学数据

        Args:
            years: 目标年份列表
            provinces: 目标省份列表，如果为空或None则获取所有省份数据
            science_types: 目标科类列表

        Yields:
            大学数据字典
        """
        years = years or self.config.TARGET_YEARS
        science_types = science_types or list(self.config.SCIENCE_TYPES.keys())

        # 如果provinces为空或None，使用空字符串表示获取所有省份数据
        if not provinces:
            provinces = [""]  # 空字符串表示所有省份
            logger.info(f"开始爬取大学数据: {len(years)}年份 x 所有省份 x {len(science_types)}科类")
        else:
            logger.info(f"开始爬取大学数据: {len(years)}年份 x {len(provinces)}省份 x {len(science_types)}科类")

        # 顺序执行任务（避免并发问题）

        for year in years:
            async for university_data in self.crawl_universities(year, province='', science='', batch=''):
                yield university_data

    async def crawl_university_majors(self, university_code: str, university_name: str = "",
                                      years: List[int] = None, provinces: List[str] = None,
                                      science_types: List[str] = None) -> List[Dict]:
        """
        爬取指定大学的专业数据

        Args:
            university_code: 大学代码
            university_name: 大学名称
            years: 目标年份列表
            provinces: 目标省份列表，如果为空或None则获取所有省份数据
            science_types: 目标科类列表

        Returns:
            专业数据列表
        """
        years = years or self.config.TARGET_YEARS
        science_types = science_types or list(self.config.SCIENCE_TYPES.keys())

        # 如果provinces为空或None，使用空字符串表示获取所有省份数据
        if not provinces:
            provinces = [""]  # 空字符串表示所有省份

        all_majors = []

        for year in years:
            for province in provinces:
                for science in science_types:
                    majors = await self.crawl_majors(year, province, science, university_code, university_name)
                    all_majors.extend(majors)
                    await asyncio.sleep(self.config.REQUEST_DELAY)

        return all_majors

    async def get_session_stats(self) -> Dict:
        """获取当前会话统计信息"""
        return self.session_stats.copy()

    async def reset_session_stats(self):
        """重置会话统计信息"""
        self.session_stats = {
            "total_requests": 0,
            "success_requests": 0,
            "failed_requests": 0,
            "total_records": 0
        }
