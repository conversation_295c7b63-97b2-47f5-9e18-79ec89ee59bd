"""
爬虫配置 - 基于API文档的配置信息
"""

import os
import asyncio
from typing import Dict, List
from dataclasses import dataclass


@dataclass
class CrawlerConfig:
    """爬虫配置类"""
    
    # API端点配置
    UNIVERSITY_API_URL = "https://applet.cqzk.com.cn/prod/history/front/history/ptwlList"
    MAJOR_API_URL = "https://applet.cqzk.com.cn/prod/history/front/history/ptwlMajorList"
    
    # 请求头配置 - 基于API文档
    SECRET = "CwnXDUjZifq/DZIhIo1O3kHARUVbP/CnPZ2n6Do432j0s5gSAt9/7zl9GZ9rO1C5p1h2ieZpUJ+CH7XBthAXXCsCF4rMsSu6DQvXdzLNBXeYnr1g7Hpcf6XT5GF1GXf+aRgmjJ/wM9MIli3ih8iLgJUg8uf3ha3DhBVVg5qi71s="
    TOKEN = "1750037640264-6776-KC6QPETTVUDFTFV-682788719"  # 需要替换为实际token

    DEFAULT_HEADERS = {
        "content-type": "application/json",
        "priority": "u=1, i",
        "secret": SECRET,
        "token": TOKEN,
    }

    # 爬取参数配置
    ROUTE_ID = "1709271134530-1784-VDTQUO7ISS5LURW-1229645147"
    DEFAULT_ROUTE_ID = ROUTE_ID  # 保持兼容性
    
    # 年份配置
    TARGET_YEARS = [2022, 2023, 2024]
    
    # 省份配置 - 动态从数据库获取
    @property
    def TARGET_PROVINCES(self):
        """动态获取目标省份列表"""
        try:
            from app.models.base import Province

            # 尝试获取数据库中的省份
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 在运行的事件循环中，返回默认值
                    return self._get_default_provinces()
                else:
                    provinces = loop.run_until_complete(Province.all().values_list("name", flat=True))
                    return list(provinces) if provinces else self._get_default_provinces()
            except RuntimeError:
                # 没有事件循环，创建新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    provinces = loop.run_until_complete(Province.all().values_list("name", flat=True))
                    return list(provinces) if provinces else self._get_default_provinces()
                finally:
                    loop.close()

        except Exception:
            # 如果数据库不可用，返回默认省份列表
            return self._get_default_provinces()

    def _get_default_provinces(self):
        """获取默认省份列表"""
        return [
            "北京", "天津", "河北", "山西", "内蒙古",
            "辽宁", "吉林", "黑龙江",
            "上海", "江苏", "浙江", "安徽", "福建", "江西", "山东",
            "河南", "湖北", "湖南",
            "广东", "广西", "海南",
            "重庆", "四川", "贵州", "云南", "西藏",
            "陕西", "甘肃", "青海", "宁夏", "新疆","香港"
        ]
    
    # 科类配置 - 动态从数据库获取
    @property
    def SCIENCE_TYPES(self):
        """动态获取科类配置"""
        try:
            from app.models.base import ScienceType

            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    return self._get_default_science_types()
                else:
                    science_types = loop.run_until_complete(
                        ScienceType.all().values_list("code", "name")
                    )
                    return dict(science_types) if science_types else self._get_default_science_types()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    science_types = loop.run_until_complete(
                        ScienceType.all().values_list("code", "name")
                    )
                    return dict(science_types) if science_types else self._get_default_science_types()
                finally:
                    loop.close()

        except Exception:
            return self._get_default_science_types()

    def _get_default_science_types(self):
        """获取默认科类配置"""
        return {
            "1": "历史",
            "5": "物理",
        }
    
    # 批次配置 - 动态从数据库获取
    @property
    def BATCH_TYPES(self):
        """动态获取批次配置"""
        try:
            from app.models.base import Batch

            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    return self._get_default_batch_types()
                else:
                    batches = loop.run_until_complete(
                        Batch.all().values_list("code", "name")
                    )
                    return dict(batches) if batches else self._get_default_batch_types()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    batches = loop.run_until_complete(
                        Batch.all().values_list("code", "name")
                    )
                    return dict(batches) if batches else self._get_default_batch_types()
                finally:
                    loop.close()

        except Exception:
            return self._get_default_batch_types()

    def _get_default_batch_types(self):
        """获取默认批次配置"""
        return {
            "0": "本科提前批A段",
            "1": "本科提前批B段",
            "2": "本科批",
            "5": "高职专科批"
        }
    
    # 并发控制
    MAX_CONCURRENT_REQUESTS = 5
    REQUEST_DELAY = 1.0  # 请求间隔(秒)
    RETRY_TIMES = 3
    TIMEOUT = 30
    
    # 数据处理
    BATCH_SIZE = 100  # 批量处理大小
    
    @classmethod
    def get_university_request_params(cls, year: str, province: str, science: str, 
                                    batch: str = "1", page_no: int = 1) -> Dict:
        """
        获取学校查询API的请求参数
        基于API文档中的请求参数结构
        """
        return {
            "routeId": cls.DEFAULT_ROUTE_ID,
            "year": str(year),
            "science": str(science),
            "province": province, # 空表示所有学校
            "collage": "",  # 空表示查询所有学校
            "is211": "",
            "is985": "",
            "sfyldx": "",
            "majorName": "",
            "majorNameNoLike": "",
            "front_WC": 1,
            "batch": str(batch),
            "pageNo": page_no
        }
    
    @classmethod
    def get_major_request_params(cls, year: str, province: str, science: str,
                               collage: str, batch: str = "", university_name: str = "") -> Dict:
        """
        获取专业查询API的请求参数
        基于API文档中的请求参数结构

        Args:
            year: 年份
            province: 省份
            science: 科类
            collage: 大学代码
            batch: 批次
            university_name: 大学名称（用于sexRemark字段）
        """
        return {
            "routeId": cls.DEFAULT_ROUTE_ID,
            "year": str(year),
            "science": str(science),
            "province": province,
            "collage": collage,
            "is211": "",
            "is985": "",
            "sfyldx": "",
            "majorName": "",
            "majorNameNoLike": "",
            "front_WC": 1,
            "batch": str(batch),
            "minScoreLine": None,
            "sexRemark": university_name  # 根据API文档，这里应该是大学名称
        }
    
    @classmethod
    def get_headers_with_token(cls, token: str) -> Dict:
        """获取包含实际token的请求头"""
        headers = cls.DEFAULT_HEADERS.copy()
        headers["token"] = token
        return headers
    
    @classmethod
    def from_env(cls) -> 'CrawlerConfig':
        """从环境变量创建配置"""
        config = cls()
        
        # 从环境变量读取配置
        if os.getenv("GAOKAO_API_TOKEN"):
            config.DEFAULT_HEADERS["token"] = os.getenv("GAOKAO_API_TOKEN")
        
        if os.getenv("GAOKAO_SECRET"):
            config.DEFAULT_HEADERS["secret"] = os.getenv("GAOKAO_SECRET")
            
        if os.getenv("GAOKAO_ROUTE_ID"):
            config.DEFAULT_ROUTE_ID = os.getenv("GAOKAO_ROUTE_ID")
        
        # 并发控制配置
        if os.getenv("MAX_CONCURRENT_REQUESTS"):
            config.MAX_CONCURRENT_REQUESTS = int(os.getenv("MAX_CONCURRENT_REQUESTS"))
            
        if os.getenv("REQUEST_DELAY"):
            config.REQUEST_DELAY = float(os.getenv("REQUEST_DELAY"))
        
        return config
