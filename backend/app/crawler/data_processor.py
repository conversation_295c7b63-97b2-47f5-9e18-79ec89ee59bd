"""
数据处理器 - 处理API返回的原始数据并转换为数据库模型
"""

from typing import Dict, List, Optional, Tuple
from loguru import logger
from datetime import datetime

from app.models import (
    University, Major, AdmissionRecord, MajorAdmissionRecord,
    Province, Batch, ScienceType, SubjectRequirement
)


class DataProcessor:
    """
    数据处理器类
    负责将API返回的原始数据转换为数据库模型实例
    """
    
    def __init__(self):
        self.stats = {
            "processed_universities": 0,
            "processed_majors": 0,
            "processed_admission_records": 0,
            "processed_major_records": 0,
            "errors": 0
        }
    
    def _safe_int(self, value: any, default: int = None) -> Optional[int]:
        """安全转换为整数"""
        if value is None or value == "" or value == "0":
            return default
        try:
            return int(str(value))
        except (ValueError, TypeError):
            return default
    
    def _safe_bool(self, value: any) -> bool:
        """安全转换为布尔值"""
        if value is None or value == "":
            return False
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ["1", "true", "yes", "是"]
        if isinstance(value, (int, float)):
            return bool(value)
        return False
    
    def _safe_str(self, value: any, max_length: int = None) -> Optional[str]:
        """安全转换为字符串"""
        if value is None:
            return None
        
        result = str(value).strip()
        if result == "" or result.lower() == "null":
            return None
        
        if max_length and len(result) > max_length:
            result = result[:max_length]
        
        return result
    
    async def process_university_data(self, raw_data: Dict) -> Tuple[University, AdmissionRecord]:
        """
        处理大学数据
        
        Args:
            raw_data: API返回的原始大学数据
            
        Returns:
            (University实例, AdmissionRecord实例)
        """
        try:
            # 处理大学基础信息
            university_data = {
                "collage_code": self._safe_str(raw_data.get("collage"), 20),
                "collage_standard_code": self._safe_str(raw_data.get("collageCode"), 20),
                "name": self._safe_str(raw_data.get("collageName"), 200),
                "province_name": self._safe_str(raw_data.get("province"), 50),
                "is_985": self._safe_bool(raw_data.get("is985")),
                "is_211": self._safe_bool(raw_data.get("is211")),
                "is_ylyxjsgx": self._safe_bool(raw_data.get("isYLYXJSGX")),
                "is_ylxkjsgx": self._safe_bool(raw_data.get("isYLXKJSGX")),
                "is_soldier": self._safe_bool(raw_data.get("isSoldier")),
                "sfyldx": self._safe_str(raw_data.get("sfyldx"), 10),
                "university_type": self._safe_str(raw_data.get("yxxz"), 50),
                "website": self._safe_str(raw_data.get("website"), 500),
                "zhaosheng_url": self._safe_str(raw_data.get("zhaosheng"), 500),
                "remarks": self._safe_str(raw_data.get("remarks")),
                "sex_remark": self._safe_str(raw_data.get("sexRemark"), 200),
            }
            
            # 获取或创建大学记录
            try:
                # 从defaults中移除查询字段，避免重复参数
                defaults_data = university_data.copy()
                collage_code = defaults_data.pop("collage_code")

                university, created = await University.get_or_create(
                    collage_code=collage_code,
                    defaults=defaults_data
                )
            except Exception as e:
                # 如果是唯一约束错误，尝试获取现有记录
                if "UNIQUE constraint failed" in str(e):
                    try:
                        # 尝试通过collage_code获取
                        university = await University.get(collage_code=university_data["collage_code"])
                        created = False
                    except:
                        # 如果通过collage_code找不到，尝试通过collage_standard_code获取
                        try:
                            university = await University.get(collage_standard_code=university_data["collage_standard_code"])
                            created = False
                        except:
                            # 如果都找不到，记录错误并跳过
                            logger.error(f"无法找到或创建大学记录: {university_data['collageName']}")
                            return None, None
                else:
                    raise
            
            if not created:
                # 更新现有记录
                for key, value in university_data.items():
                    if value is not None:
                        setattr(university, key, value)
                await university.save()
            
            # 处理录取记录
            admission_data = {
                "university": university,
                "year": self._safe_int(raw_data.get("year")),
                "province_name": self._safe_str(raw_data.get("province"), 50),
                "batch_code": self._safe_str(raw_data.get("batch"), 10),
                "batch_name": self._safe_str(raw_data.get("batchName"), 50),
                "science_code": self._safe_str(raw_data.get("science"), 10),
                "science_name": self._safe_str(raw_data.get("scienceName"), 50),
                "max_score": self._safe_int(raw_data.get("maxScore")),
                "max_score_rank": self._safe_int(raw_data.get("maxScoreRank")),
                "min_score": self._safe_int(raw_data.get("minScore")),
                "min_score_rank": self._safe_int(raw_data.get("minScoreRank")),
                "avg_score": self._safe_int(raw_data.get("avgScore")),
                "avg_score_line": self._safe_int(raw_data.get("avgScoreLine")),
                "min_score_line": self._safe_int(raw_data.get("minScoreLine")),
                "collage_plan": self._safe_int(raw_data.get("collagePlan")),
                "collage_real": self._safe_int(raw_data.get("collageReal")),
                "remarks": self._safe_str(raw_data.get("remarks")),
                "sex_remark": self._safe_str(raw_data.get("sexRemark"), 200),
                "raw_data": raw_data
            }
            
            # 关联外键
            if admission_data["province_name"]:
                province, _ = await Province.get_or_create(name=admission_data["province_name"])
                admission_data["province"] = province
            
            if admission_data["batch_code"]:
                batch, _ = await Batch.get_or_create(
                    code=admission_data["batch_code"],
                    defaults={"name": admission_data["batch_name"] or f"批次{admission_data['batch_code']}"}
                )
                admission_data["batch"] = batch
            
            if admission_data["science_code"]:
                science_type, _ = await ScienceType.get_or_create(
                    code=admission_data["science_code"],
                    defaults={"name": admission_data["science_name"] or f"科类{admission_data['science_code']}"}
                )
                admission_data["science_type"] = science_type
            
            # 创建或更新录取记录
            # 从defaults中移除查询字段，避免重复参数
            defaults_data = admission_data.copy()
            defaults_data.pop("university", None)      # 移除university字段
            defaults_data.pop("year", None)            # 移除year字段
            defaults_data.pop("province_name", None)   # 移除province_name字段
            defaults_data.pop("batch_code", None)      # 移除batch_code字段
            defaults_data.pop("science_code", None)    # 移除science_code字段

            admission_record, created = await AdmissionRecord.get_or_create(
                university=university,
                year=admission_data["year"],
                province_name=admission_data["province_name"],
                batch_code=admission_data["batch_code"],
                science_code=admission_data["science_code"],
                defaults=defaults_data
            )
            
            if not created:
                # 更新现有记录
                for key, value in admission_data.items():
                    if key not in ["university"] and value is not None:
                        setattr(admission_record, key, value)
                await admission_record.save()
            
            self.stats["processed_universities"] += 1
            self.stats["processed_admission_records"] += 1
            
            return university, admission_record
            
        except Exception as e:
            logger.error(f"处理大学数据时出错: {e}, 数据: {raw_data}")
            self.stats["errors"] += 1
            raise
    
    async def process_major_data(self, raw_data: Dict, university: University) -> Tuple[Major, MajorAdmissionRecord]:
        """
        处理专业数据
        
        Args:
            raw_data: API返回的原始专业数据
            university: 关联的大学实例
            
        Returns:
            (Major实例, MajorAdmissionRecord实例)
        """
        try:
            # 处理专业基础信息
            major_data = {
                "major_code": self._safe_str(raw_data.get("major"), 20),
                "major_standard_code": self._safe_str(raw_data.get("majorCode"), 20),
                "name": self._safe_str(raw_data.get("majorName"), 200),
                "major_type": self._safe_str(raw_data.get("majorType"), 50),
                "major_type_code": self._safe_str(raw_data.get("majorTypeCode"), 20),
                "head_subject": self._safe_str(raw_data.get("headSubject"), 100),
                "second_subject": self._safe_str(raw_data.get("secondSubject"), 100),
                "second_subject2": self._safe_str(raw_data.get("secondSubject2"), 100),
                "major_link": self._safe_str(raw_data.get("majorLink"), 500),
            }
            
            # 获取或创建专业记录
            try:
                major, created = await Major.get_or_create(
                    major_code=major_data["major_code"],
                    name=major_data["name"],
                    defaults=major_data
                )
            except Exception as e:
                # 如果是唯一约束错误，尝试通过major_code和name获取现有记录
                if "UNIQUE constraint failed" in str(e):
                    major = await Major.get(major_code=major_data["major_code"], name=major_data["name"])
                    created = False
                else:
                    logger.error(e)
            
            if not created:
                # 更新现有记录
                for key, value in major_data.items():
                    if value is not None:
                        setattr(major, key, value)
                await major.save()
            
            # 处理专业录取记录
            major_admission_data = {
                "university": university,
                "major": major,
                "year": self._safe_int(raw_data.get("year")),
                "province_name": self._safe_str(raw_data.get("province"), 50),
                "batch_code": self._safe_str(raw_data.get("batch"), 10),
                "batch_name": self._safe_str(raw_data.get("batchName"), 50),
                "science_code": self._safe_str(raw_data.get("science"), 10),
                "science_name": self._safe_str(raw_data.get("scienceName"), 50),
                "major_code": self._safe_str(raw_data.get("major"), 20),
                "major_standard_code": self._safe_str(raw_data.get("majorCode"), 20),
                "major_name": self._safe_str(raw_data.get("majorName"), 200),
                "major_name_no_like": self._safe_str(raw_data.get("majorNameNoLike"), 200),
                "major_plan": self._safe_int(raw_data.get("majorPlan")),
                "major_real": self._safe_int(raw_data.get("majorReal")),
                "major_avg_score": self._safe_int(raw_data.get("majorAvgScore")),
                "major_score": self._safe_int(raw_data.get("majorScore")),
                "major_score_rank": self._safe_int(raw_data.get("majorScoreRank")),
                "major_score_person": self._safe_int(raw_data.get("majorScorePerson")),
                "major_max_score": self._safe_int(raw_data.get("majorMaxScore")),
                "major_max_score_rank": self._safe_int(raw_data.get("majorMaxScoreRank")),
                "major_min_score": self._safe_int(raw_data.get("majorMinScore")),
                "major_min_score_rank": self._safe_int(raw_data.get("majorMinScoreRank")),
                "major_requirement": self._safe_str(raw_data.get("majorRequirement"), 100),
                "major_requirement_name": self._safe_str(raw_data.get("majorRequirementName"), 100),
                "major_link": self._safe_str(raw_data.get("majorLink"), 500),
                "remarks": self._safe_str(raw_data.get("remarks")),
                "raw_data": raw_data
            }
            
            # 关联外键
            if major_admission_data["province_name"]:
                province, _ = await Province.get_or_create(name=major_admission_data["province_name"])
                major_admission_data["province"] = province
            
            if major_admission_data["batch_code"]:
                batch, _ = await Batch.get_or_create(
                    code=major_admission_data["batch_code"],
                    defaults={"name": major_admission_data["batch_name"] or f"批次{major_admission_data['batch_code']}"}
                )
                major_admission_data["batch"] = batch
            
            if major_admission_data["science_code"]:
                science_type, _ = await ScienceType.get_or_create(
                    code=major_admission_data["science_code"],
                    defaults={"name": major_admission_data["science_name"] or f"科类{major_admission_data['science_code']}"}
                )
                major_admission_data["science_type"] = science_type
            
            if major_admission_data["major_requirement"]:
                subject_requirement, _ = await SubjectRequirement.get_or_create(
                    code=major_admission_data["major_requirement"],
                    defaults={"name": major_admission_data["major_requirement_name"] or major_admission_data["major_requirement"]}
                )
                major_admission_data["subject_requirement"] = subject_requirement
            
            # 创建或更新专业录取记录
            # 从defaults中移除查询字段，避免重复参数
            defaults_data = major_admission_data.copy()
            defaults_data.pop("university", None)      # 移除university字段
            defaults_data.pop("major", None)           # 移除major字段
            defaults_data.pop("year", None)            # 移除year字段
            defaults_data.pop("province_name", None)   # 移除province_name字段
            defaults_data.pop("batch_code", None)      # 移除batch_code字段
            defaults_data.pop("science_code", None)    # 移除science_code字段

            major_record, created = await MajorAdmissionRecord.get_or_create(
                university=university,
                major=major,
                year=major_admission_data["year"],
                province_name=major_admission_data["province_name"],
                batch_code=major_admission_data["batch_code"],
                science_code=major_admission_data["science_code"],
                defaults=defaults_data
            )
            
            if not created:
                # 更新现有记录
                for key, value in major_admission_data.items():
                    if key not in ["university", "major"] and value is not None:
                        setattr(major_record, key, value)
                await major_record.save()
            
            self.stats["processed_majors"] += 1
            self.stats["processed_major_records"] += 1
            
            return major, major_record
            
        except Exception as e:
            logger.error(f"处理专业数据时出错: {e}, 数据: {raw_data}")
            self.stats["errors"] += 1
            raise
    
    def get_stats(self) -> Dict:
        """获取处理统计信息"""
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "processed_universities": 0,
            "processed_majors": 0,
            "processed_admission_records": 0,
            "processed_major_records": 0,
            "errors": 0
        }
