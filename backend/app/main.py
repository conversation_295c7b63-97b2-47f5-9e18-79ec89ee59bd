"""
高考数据收集分析平台 - FastAPI主应用
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn

from app.core.database import init_db, close_db, create_initial_data, get_db_stats
from app.api import universities, majors, admissions, crawler, statistics, base_data
from app.api import websocket as websocket_api

# 尝试导入新功能，如果失败则跳过
try:
    from app.core.config import init_config, get_settings
    print("✅ config模块导入成功")
    config_available = True
except ImportError as e:
    print(f"⚠️ config模块导入失败: {e}")
    config_available = False

try:
    from app.core.exceptions import setup_exception_handlers
    print("✅ exceptions模块导入成功")
    exceptions_available = True
except ImportError as e:
    print(f"⚠️ exceptions模块导入失败: {e}")
    exceptions_available = False

try:
    from app.middleware.security import setup_security_middleware
    print("✅ security模块导入成功")
    security_available = True
except ImportError as e:
    print(f"⚠️ security模块导入失败: {e}")
    security_available = False

try:
    from app.services.cache_service import CacheManager
    print("✅ cache_service模块导入成功")
    cache_available = True
except ImportError as e:
    print(f"⚠️ cache_service模块导入失败: {e}")
    cache_available = False

try:
    from app.api import analytics
    print("✅ analytics模块导入成功")
    analytics_available = True
except ImportError as e:
    print(f"⚠️ analytics模块导入失败: {e}")
    analytics_available = False

# 检查是否有足够的功能可用
NEW_FEATURES_AVAILABLE = analytics_available  # 只要analytics可用就启用新功能

if config_available:
    try:
        settings = init_config()
        print("✅ 配置初始化成功")
    except Exception as e:
        print(f"⚠️ 配置初始化失败: {e}")
        config_available = False

if not config_available:
    # 使用默认配置
    class DefaultSettings:
        title = "高考数据收集分析平台"
        description = "基于API文档设计的高考院校专业数据收集、存储、分析和可视化展示平台"
        version = "0.1.0"
        debug = True

    settings = DefaultSettings()
    print("✅ 使用默认配置")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    await init_db()
    # await create_initial_data()
    print("✅ 数据库初始化完成")

    # 缓存预热（如果缓存可用）
    if cache_available:
        try:
            await CacheManager.warm_up()
            print("✅ 缓存预热完成")
        except Exception as e:
            print(f"⚠️ 缓存预热失败: {e}")

    yield

    # 关闭时清理资源
    await close_db()
    print("✅ 数据库连接已关闭")


# 创建FastAPI应用
app = FastAPI(
    title=settings.title,
    description=settings.description,
    version=settings.version,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
    debug=settings.debug
)

# 设置安全中间件和异常处理器（如果可用）
if security_available:
    try:
        setup_security_middleware(app)
        print("✅ 安全中间件配置完成")
    except Exception as e:
        print(f"⚠️ 安全中间件配置失败: {e}")

if exceptions_available:
    try:
        setup_exception_handlers(app)
        print("✅ 异常处理器配置完成")
    except Exception as e:
        print(f"⚠️ 异常处理器配置失败: {e}")

if not security_available:
    # 基础CORS配置
    from fastapi.middleware.cors import CORSMiddleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    print("✅ 基础CORS配置完成")


# 注册API路由
app.include_router(universities.router, prefix="/api/universities", tags=["大学"])
app.include_router(majors.router, prefix="/api/majors", tags=["专业"])
app.include_router(admissions.router, prefix="/api/admissions", tags=["录取数据"])
app.include_router(crawler.router, prefix="/api/crawler", tags=["数据爬取"])
app.include_router(statistics.router, prefix="/api/statistics", tags=["统计分析"])
app.include_router(base_data.router, prefix="/api/base-data", tags=["基础数据"])
app.include_router(websocket_api.router, prefix="/api/ws", tags=["WebSocket"])

# 注册数据分析路由（如果可用）
if analytics_available:
    try:
        app.include_router(analytics.router, prefix="/api/analytics", tags=["数据分析"])
        print("✅ 数据分析API路由注册完成")
    except Exception as e:
        print(f"⚠️ 数据分析API路由注册失败: {e}")
else:
    print("⚠️ 数据分析功能不可用，跳过路由注册")


@app.get("/", summary="根路径", description="API根路径，返回基本信息")
async def root():
    """根路径"""
    return {
        "message": "高考数据收集分析平台 API",
        "version": "0.1.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/api/health", summary="健康检查", description="检查API服务状态")
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        stats = await get_db_stats()
        return {
            "status": "healthy",
            "database": "connected",
            "stats": stats
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "database": "disconnected",
                "error": str(e)
            }
        )


@app.get("/api/info", summary="系统信息", description="获取系统基本信息")
async def system_info():
    """系统信息"""
    from app.crawler.config import CrawlerConfig
    
    config = CrawlerConfig()
    
    return {
        "system": {
            "name": "高考数据收集分析平台",
            "version": "0.1.0",
            "database": "SQLite",
            "orm": "Tortoise ORM"
        },
        "crawler": {
            "science_types": config.SCIENCE_TYPES,
            "batch_types": config.BATCH_TYPES,
            "target_years": config.TARGET_YEARS,
            "provinces_count": len(config.TARGET_PROVINCES)
        },
        "database": await get_db_stats()
    }


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": str(exc),
            "path": str(request.url)
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
