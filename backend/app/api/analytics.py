"""
数据分析API - 提供各种统计分析和可视化数据接口
"""

from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Query, HTTPException, status
from pydantic import BaseModel, Field

from app.services.analytics_service import analytics_service
from app.middleware.security import InputValidator

router = APIRouter()


# 响应模型
class OverviewStatsResponse(BaseModel):
    """概览统计响应"""
    basic_stats: Dict[str, int]
    coverage: Dict[str, Any]
    last_updated: str


class TrendDataPoint(BaseModel):
    """趋势数据点"""
    year: int
    avg_min_score: float
    avg_avg_score: float
    avg_max_score: float
    best_rank: int
    avg_rank: int


class TrendsResponse(BaseModel):
    """趋势分析响应"""
    trends: List[TrendDataPoint]
    summary: Dict[str, Any]


class UniversityRanking(BaseModel):
    """大学排名"""
    rank: int
    university_code: str
    university_name: str
    is_985: bool
    is_211: bool
    university_type: str
    location: str
    min_score: Optional[float]
    avg_score: Optional[float]
    max_score: Optional[float]
    min_rank: Optional[int]
    province: str


class MajorPopularity(BaseModel):
    """专业热度"""
    rank: int
    major_code: str
    major_name: str
    category: str
    record_count: int
    avg_min_score: float
    avg_max_score: float
    total_enrollment: int


class ProvinceCompetition(BaseModel):
    """省份竞争"""
    rank: int
    province: str
    university_count: int
    avg_min_score: float
    avg_max_score: float
    score_range: float
    best_rank: int
    worst_rank: int
    rank_range: int
    competition_level: str


class ScoreDistributionItem(BaseModel):
    """分数分布项"""
    range: str
    count: int


class ScoreDistribution(BaseModel):
    """分数分布"""
    type: str
    data: List[ScoreDistributionItem]


class ScoreDistributionResponse(BaseModel):
    """分数分布响应"""
    distribution: List[ScoreDistribution]
    statistics: Dict[str, Any]


@router.get(
    "/overview",
    summary="获取数据概览统计",
    description="获取系统中所有数据的概览统计信息，包括大学、专业、录取记录等基础统计"
)
async def get_overview_statistics():
    """获取数据概览统计"""
    try:
        stats = await analytics_service.get_overview_statistics()
        if not stats:
            # 返回默认的空统计数据
            stats = {
                "basic_stats": {
                    "total_universities": 0,
                    "total_majors": 0,
                    "total_admission_records": 0,
                    "total_major_records": 0,
                    "universities_985": 0,
                    "universities_211": 0,
                },
                "coverage": {
                    "year_range": "无数据",
                    "total_years": 0,
                    "province_count": 0,
                },
                "last_updated": "2025-06-19T13:00:00"
            }

        return {
            "success": True,
            "data": stats,
            "message": "数据概览获取成功"
        }
    except Exception as e:
        return {
            "success": False,
            "error": {
                "code": 1000,
                "message": f"获取概览统计失败: {str(e)}",
                "type": "unknown_error"
            },
            "data": None
        }


@router.get(
    "/trends",
    summary="获取录取趋势分析",
    description="获取指定年份范围内的录取分数和排名趋势分析"
)
async def get_admission_trends(
    year_start: int = Query(2020, ge=2000, le=2030, description="起始年份"),
    year_end: int = Query(2024, ge=2000, le=2030, description="结束年份")
):
    """获取录取趋势分析"""
    try:
        # 验证年份范围
        if year_start > year_end:
            return {
                "success": False,
                "error": {"message": "起始年份不能大于结束年份"},
                "data": None
            }

        if year_end - year_start > 10:
            return {
                "success": False,
                "error": {"message": "年份范围不能超过10年"},
                "data": None
            }

        trends = await analytics_service.get_admission_trends(year_start, year_end)
        return {
            "success": True,
            "data": trends,
            "message": "趋势分析获取成功"
        }
    except Exception as e:
        return {
            "success": False,
            "error": {
                "code": 1001,
                "message": f"获取录取趋势失败: {str(e)}",
                "type": "trends_error"
            },
            "data": None
        }


@router.get(
    "/rankings",
    summary="获取大学排名",
    description="根据录取分数和排名获取大学排名列表"
)
async def get_university_rankings(
    year: int = Query(2024, ge=2000, le=2030, description="年份"),
    science_type: str = Query("5", regex="^[15]$", description="科类：1-历史类，5-物理类"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制")
):
    """获取大学排名"""
    try:
        # 验证输入
        if not InputValidator.validate_year(year):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的年份"
            )
        
        if not InputValidator.validate_science_type(science_type):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的科类"
            )
        
        rankings = await analytics_service.get_university_rankings(year, science_type, limit)
        return rankings
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取大学排名失败: {str(e)}"
        )


@router.get(
    "/majors/popularity",
    response_model=List[MajorPopularity],
    summary="获取专业热度排行",
    description="根据录取记录数量和招生人数获取专业热度排行"
)
async def get_major_popularity(
    year: int = Query(2024, ge=2000, le=2030, description="年份"),
    limit: int = Query(30, ge=1, le=100, description="返回数量限制")
):
    """获取专业热度排行"""
    try:
        if not InputValidator.validate_year(year):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的年份"
            )
        
        popularity = await analytics_service.get_major_popularity(year, limit)
        return popularity
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取专业热度失败: {str(e)}"
        )


@router.get(
    "/provinces/competition",
    response_model=List[ProvinceCompetition],
    summary="获取省份竞争分析",
    description="分析各省份的录取竞争激烈程度"
)
async def get_province_competition(
    year: int = Query(2024, ge=2000, le=2030, description="年份"),
    science_type: str = Query("5", regex="^[15]$", description="科类：1-历史类，5-物理类")
):
    """获取省份竞争分析"""
    try:
        if not InputValidator.validate_year(year):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的年份"
            )
        
        if not InputValidator.validate_science_type(science_type):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的科类"
            )
        
        competition = await analytics_service.get_province_competition(year, science_type)
        return competition
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取省份竞争分析失败: {str(e)}"
        )


@router.get(
    "/scores/distribution",
    response_model=ScoreDistributionResponse,
    summary="获取分数分布分析",
    description="获取指定条件下的录取分数分布统计"
)
async def get_score_distribution(
    year: int = Query(2024, ge=2000, le=2030, description="年份"),
    province: str = Query("河北", description="省份"),
    science_type: str = Query("5", regex="^[15]$", description="科类：1-历史类，5-物理类")
):
    """获取分数分布分析"""
    try:
        if not InputValidator.validate_year(year):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的年份"
            )
        
        if not InputValidator.validate_province(province):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的省份"
            )
        
        if not InputValidator.validate_science_type(science_type):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的科类"
            )
        
        distribution = await analytics_service.get_score_distribution(year, province, science_type)
        return distribution
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分数分布失败: {str(e)}"
        )


@router.post(
    "/cache/clear",
    summary="清除分析缓存",
    description="清除所有分析相关的缓存数据，强制重新计算"
)
async def clear_analytics_cache():
    """清除分析缓存"""
    try:
        from app.services.cache_service import cache_service
        cleared = await cache_service.clear_pattern("analytics:*")
        return {
            "message": "分析缓存已清除",
            "cleared_keys": cleared
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清除缓存失败: {str(e)}"
        )
