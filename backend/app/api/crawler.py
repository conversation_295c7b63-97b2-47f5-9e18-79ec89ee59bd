"""
爬虫相关API接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from loguru import logger
from pydantic import BaseModel
from typing import Optional, List
import asyncio
from datetime import datetime

# 简化导入，避免循环依赖
try:
    from app.crawler import <PERSON><PERSON><PERSON><PERSON>pider, CrawlerConfig
    from app.models.system import CrawlLog, CrawlStatus, DataType
    from app.services.task_manager import task_manager, TaskController
    from app.websocket import WebSocketLogger
except ImportError:
    # 如果导入失败，创建简单的替代品
    class CrawlerConfig:
        def __init__(self):
            self.UNIVERSITY_API_URL = "https://api.gaokao.cn/university"
            self.MAJOR_API_URL = "https://api.gaokao.cn/major"
            self.TARGET_YEARS = [2023, 2024]
            self.TARGET_PROVINCES = ["北京", "上海"]
            self.SCIENCE_TYPES = {"理科": "science", "文科": "liberal"}
            self.BATCH_TYPES = ["本科一批", "本科二批"]
            self.MAX_CONCURRENT_REQUESTS = 5
            self.REQUEST_DELAY = 1.0
            self.RETRY_TIMES = 3
            self.TIMEOUT = 30
            self.BATCH_SIZE = 100


router = APIRouter()


class CrawlerConfigRequest(BaseModel):
    """爬虫配置请求"""
    base_url: Optional[str] = None
    api_token: Optional[str] = None
    timeout: Optional[int] = None
    request_delay: Optional[float] = None
    max_retries: Optional[int] = None
    concurrent_limit: Optional[int] = None
    headers: Optional[List[dict]] = None
    proxy_enabled: Optional[bool] = None
    proxy_type: Optional[str] = None
    proxy_host: Optional[str] = None
    proxy_port: Optional[int] = None
    proxy_username: Optional[str] = None
    proxy_password: Optional[str] = None
    schedule_enabled: Optional[bool] = None
    schedule_type: Optional[str] = None
    schedule_time: Optional[str] = None
    schedule_weekday: Optional[int] = None
    schedule_cron: Optional[str] = None


class CrawlTaskRequest(BaseModel):
    """爬取任务请求"""
    years: Optional[List[int]] = None
    provinces: Optional[List[str]] = None
    science_types: Optional[List[str]] = None
    batch_types: Optional[List[str]] = None
    university_codes: Optional[List[str]] = None


class CrawlTaskResponse(BaseModel):
    """爬取任务响应"""
    task_id: str
    message: str
    status: str


@router.get("/config", summary="获取爬虫配置")
async def get_crawler_config():
    """获取爬虫配置信息"""
    try:
        from app.core.config import get_settings
        settings = get_settings()
        crawler_config = settings.crawler

        return {
            "success": True,
            "data": {
                "base_url": crawler_config.base_url,
                "api_token": crawler_config.api_token,
                "timeout": crawler_config.timeout,
                "request_delay": crawler_config.request_delay,
                "max_retries": crawler_config.max_retries,
                "concurrent_limit": crawler_config.concurrent_limit,
                "headers": [
                    {"key": "User-Agent", "value": crawler_config.user_agent},
                    {"key": "Accept", "value": crawler_config.accept}
                ],
                "proxy_enabled": crawler_config.proxy_enabled,
                "schedule_enabled": crawler_config.schedule_enabled,
                "api_endpoints": {
                    "university": crawler_config.university_endpoint,
                    "major": crawler_config.major_endpoint
                },
                "target_years": crawler_config.target_years,
                "provinces": crawler_config.provinces,
                "science_types": crawler_config.science_types,
                "batch_types": crawler_config.batch_types
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取爬虫配置失败: {str(e)}")


@router.post("/config")
async def update_crawler_config(config_request: CrawlerConfigRequest):
    """更新爬虫配置"""
    try:
        # 这里可以实现配置更新逻辑
        # 将配置保存到数据库或配置文件

        return {
            "success": True,
            "message": "配置更新成功",
            "data": config_request.dict(exclude_none=True)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.post("/test")
async def test_post_route():
    """测试POST路由"""
    return {"message": "POST路由工作正常"}


@router.post("/universities/start", response_model=CrawlTaskResponse, summary="启动大学数据爬取")
async def start_university_crawl(request: CrawlTaskRequest):
    """启动大学数据爬取任务"""
    try:
        # 生成任务ID
        task_id = f"university_crawl_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 创建爬取日志
        crawl_log = await CrawlLog.create(
            task_id=task_id,
            task_name="大学数据爬取",
            data_type=DataType.UNIVERSITY,
            target_year=request.years[0] if request.years else None,
            target_province=request.provinces[0] if request.provinces else None,
            crawl_params=request.dict(),
            status=CrawlStatus.PENDING
        )

        # 处理空参数：如果provinces为空列表，传递None让爬虫获取所有数据
        provinces = request.provinces if request.provinces else None
        science_types = request.science_types if request.science_types else None
        batch_types = request.batch_types if request.batch_types else None

        # 使用任务管理器启动任务
        success = await task_manager.start_task(
            task_id,
            crawl_universities_task,
            request.years,
            provinces,
            science_types,
            batch_types
        )

        if not success:
            raise HTTPException(status_code=400, detail="任务启动失败，可能已有同名任务在运行")

        return CrawlTaskResponse(
            task_id=task_id,
            message="大学数据爬取任务已启动",
            status="started"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动大学数据爬取失败: {str(e)}")


@router.post("/majors/start", response_model=CrawlTaskResponse, summary="启动专业数据爬取")
async def start_major_crawl(request: CrawlTaskRequest):
    """启动专业数据爬取任务"""
    try:
        # 生成任务ID
        task_id = f"major_crawl_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 创建爬取日志
        crawl_log = await CrawlLog.create(
            task_id=task_id,
            task_name="专业数据爬取",
            data_type=DataType.MAJOR,
            target_year=request.years[0] if request.years else None,
            target_province=request.provinces[0] if request.provinces else None,
            crawl_params=request.dict(),
            status=CrawlStatus.PENDING
        )

        # 处理空参数：如果provinces为空列表，传递None让爬虫获取所有数据
        provinces = request.provinces if request.provinces else None
        science_types = request.science_types if request.science_types else None

        # 使用任务管理器启动任务
        success = await task_manager.start_task(
            task_id,
            crawl_majors_task,
            request.years,
            provinces,
            science_types,
            request.university_codes
        )

        if not success:
            raise HTTPException(status_code=400, detail="任务启动失败，可能已有同名任务在运行")

        return CrawlTaskResponse(
            task_id=task_id,
            message="专业数据爬取任务已启动",
            status="started"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动专业数据爬取失败: {str(e)}")


@router.get("/tasks/{task_id}", summary="获取爬取任务状态")
async def get_crawl_task_status(task_id: str):
    """获取指定爬取任务的状态"""
    try:
        crawl_log = await CrawlLog.get_or_none(task_id=task_id)
        if not crawl_log:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return {
            "task_id": crawl_log.task_id,
            "task_name": crawl_log.task_name,
            "data_type": crawl_log.data_type,
            "status": crawl_log.status,
            "start_time": crawl_log.start_time,
            "end_time": crawl_log.end_time,
            "duration": crawl_log.duration,
            "total_requests": crawl_log.total_requests,
            "success_requests": crawl_log.success_requests,
            "failed_requests": crawl_log.failed_requests,
            "total_records": crawl_log.total_records,
            "new_records": crawl_log.new_records,
            "updated_records": crawl_log.updated_records,
            "error_message": crawl_log.error_message,
            "success_rate": crawl_log.success_rate
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.get("/tasks", summary="获取爬取任务列表")
async def get_crawl_tasks(
    page: int = 1,
    size: int = 20,
    data_type: Optional[str] = None,
    status: Optional[str] = None
):
    """获取爬取任务列表"""
    try:
        # 构建查询
        query = CrawlLog.all()

        if data_type:
            query = query.filter(data_type=data_type)
        if status:
            query = query.filter(status=status)

        # 获取总数
        total = await query.count()

        # 分页查询
        offset = (page - 1) * size
        tasks = await query.offset(offset).limit(size).order_by("-created_at").all()

        return {
            "data": tasks,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.delete("/tasks/{task_id}", summary="删除爬取任务")
async def delete_crawl_task(task_id: str):
    """删除指定的爬取任务"""
    try:
        # 检查任务是否存在
        crawl_log = await CrawlLog.get_or_none(task_id=task_id)
        if not crawl_log:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 检查任务是否正在运行
        if crawl_log.status in [CrawlStatus.RUNNING, CrawlStatus.PENDING]:
            # 如果任务正在运行，先尝试取消
            controller = task_manager.get_task_controller(task_id)
            if controller:
                await task_manager.cancel_task(task_id)
                # 等待一小段时间让任务完成取消
                import asyncio
                await asyncio.sleep(1)

        # 从数据库中删除任务记录
        await crawl_log.delete()

        # 从任务管理器中清理相关资源
        task_manager._cleanup_task(task_id)

        return {
            "success": True,
            "message": f"任务 {task_id} 已成功删除",
            "task_id": task_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除任务失败: {str(e)}")


async def crawl_universities_task(
    task_id: str,
    controller: TaskController,
    ws_logger: WebSocketLogger,
    years: Optional[List[int]] = None,
    provinces: Optional[List[str]] = None,
    science_types: Optional[List[str]] = None,
    batch_types: Optional[List[str]] = None
):
    """大学数据爬取后台任务"""
    crawl_log = await CrawlLog.get(task_id=task_id)

    try:
        await ws_logger.info("开始大学数据爬取任务")

        # 执行爬取
        config = CrawlerConfig()
        async with GaokaoSpider(config) as spider:
            count = 0
            total_estimated = 0

            # 估算总数（用于进度显示）
            years_list = years or config.TARGET_YEARS
            provinces_list = provinces or config.TARGET_PROVINCES
            science_types_list = science_types or list(config.SCIENCE_TYPES.keys())
            total_estimated = len(years_list) * len(provinces_list) * len(science_types_list) * 50  # 估算每个组合50条记录

            await ws_logger.info(f"预计爬取 {total_estimated} 条大学记录")

            async for university_data in spider.crawl_all_universities(
                years=years_list,
                provinces=provinces_list,
                science_types=science_types_list
            ):
                # 检查任务状态
                await controller.wait_if_paused()
                if controller.is_cancelled():
                    await ws_logger.warning("任务被取消")
                    return

                # 处理数据
                from app.crawler.data_processor import DataProcessor
                processor = DataProcessor()
                result = await processor.process_university_data(university_data)
                if result and result[0]:  # 检查是否成功处理
                    count += 1

                # 更新进度
                if count % 10 == 0:  # 每10条记录更新一次进度
                    await task_manager.update_progress(task_id, count, total_estimated, f"已处理 {count} 条大学记录")
                    await ws_logger.info(f"已处理 {count} 条大学记录")

            # 获取统计信息
            stats = await spider.get_session_stats()

            # 更新任务状态
            crawl_log.status = "success"
            crawl_log.end_time = datetime.now()

            # 安全地计算持续时间
            if crawl_log.start_time and crawl_log.end_time:
                try:
                    # 确保时区一致性
                    start_time = crawl_log.start_time.replace(tzinfo=None) if crawl_log.start_time.tzinfo else crawl_log.start_time
                    end_time = crawl_log.end_time.replace(tzinfo=None) if crawl_log.end_time.tzinfo else crawl_log.end_time
                    crawl_log.duration = int((end_time - start_time).total_seconds())
                except Exception as e:
                    logger.warning(f"计算任务持续时间失败: {e}")
                    crawl_log.duration = 0
            crawl_log.total_requests = stats["total_requests"]
            crawl_log.success_requests = stats["success_requests"]
            crawl_log.failed_requests = stats["failed_requests"]
            crawl_log.total_records = stats["total_records"]
            crawl_log.new_records = count
            await crawl_log.save()

            await ws_logger.success(f"大学数据爬取完成，共处理 {count} 条记录")

    except Exception as e:
        # 更新任务状态为失败
        crawl_log.status = "failed"
        crawl_log.end_time = datetime.now()

        # 安全地计算持续时间
        if crawl_log.start_time and crawl_log.end_time:
            try:
                # 确保时区一致性
                start_time = crawl_log.start_time.replace(tzinfo=None) if crawl_log.start_time.tzinfo else crawl_log.start_time
                end_time = crawl_log.end_time.replace(tzinfo=None) if crawl_log.end_time.tzinfo else crawl_log.end_time
                crawl_log.duration = int((end_time - start_time).total_seconds())
            except Exception as duration_error:
                logger.warning(f"计算任务持续时间失败: {duration_error}")
                crawl_log.duration = 0

        crawl_log.error_message = str(e)
        await crawl_log.save()
        await ws_logger.error(f"大学数据爬取失败: {str(e)}")
        raise


async def crawl_majors_task(
    task_id: str,
    controller: TaskController,
    ws_logger: WebSocketLogger,
    years: Optional[List[int]] = None,
    provinces: Optional[List[str]] = None,
    science_types: Optional[List[str]] = None,
    university_codes: Optional[List[str]] = None
):
    """专业数据爬取后台任务"""
    crawl_log = await CrawlLog.get(task_id=task_id)

    try:
        await ws_logger.info("开始专业数据爬取任务")

        # 执行爬取
        config = CrawlerConfig()
        async with GaokaoSpider(config) as spider:
            count = 0

            # 如果没有指定大学代码，先获取所有大学
            if not university_codes:
                from app.models import University
                universities = await University.all().values_list("collage_code", flat=True)
                university_codes = [code for code in universities if code]

            total_universities = len(university_codes)
            await ws_logger.info(f"准备爬取 {total_universities} 所大学的专业数据")

            # 爬取每个大学的专业数据
            for i, university_code in enumerate(university_codes):
                # 检查任务状态
                await controller.wait_if_paused()
                if controller.is_cancelled():
                    await ws_logger.warning("任务被取消")
                    return

                # 获取大学信息
                from app.models import University
                university = await University.get_or_none(collage_code=university_code)
                university_name = university.name if university else ""

                await ws_logger.info(f"正在爬取大学 {university_code}({university_name}) 的专业数据 ({i+1}/{total_universities})")

                majors = await spider.crawl_university_majors(
                    university_code=university_code,
                    university_name=university_name,
                    years=years or config.TARGET_YEARS,
                    provinces=provinces or config.TARGET_PROVINCES,
                    science_types=science_types or list(config.SCIENCE_TYPES.keys())
                )

                # 处理数据
                from app.crawler.data_processor import DataProcessor

                processor = DataProcessor()

                if university:
                    for major_data in majors:
                        result = await processor.process_major_data(major_data, university)
                        if result and result[0]:  # 检查是否成功处理
                            count += 1
                else:
                    await ws_logger.warning(f"未找到大学信息: {university_code}")

                # 更新进度
                await task_manager.update_progress(task_id, i+1, total_universities, f"已完成 {i+1}/{total_universities} 所大学，共处理 {count} 条专业记录")

            # 获取统计信息
            stats = await spider.get_session_stats()

            # 更新任务状态
            crawl_log.status = "success"
            crawl_log.end_time = datetime.now()

            # 安全地计算持续时间
            if crawl_log.start_time and crawl_log.end_time:
                try:
                    # 确保时区一致性
                    start_time = crawl_log.start_time.replace(tzinfo=None) if crawl_log.start_time.tzinfo else crawl_log.start_time
                    end_time = crawl_log.end_time.replace(tzinfo=None) if crawl_log.end_time.tzinfo else crawl_log.end_time
                    crawl_log.duration = int((end_time - start_time).total_seconds())
                except Exception as e:
                    logger.warning(f"计算任务持续时间失败: {e}")
                    crawl_log.duration = 0
            crawl_log.total_requests = stats["total_requests"]
            crawl_log.success_requests = stats["success_requests"]
            crawl_log.failed_requests = stats["failed_requests"]
            crawl_log.total_records = stats["total_records"]
            crawl_log.new_records = count
            await crawl_log.save()

            await ws_logger.success(f"专业数据爬取完成，共处理 {count} 条记录")

    except Exception as e:
        # 更新任务状态为失败
        crawl_log.status = "failed"
        crawl_log.end_time = datetime.now()

        # 安全地计算持续时间
        if crawl_log.start_time and crawl_log.end_time:
            try:
                # 确保时区一致性
                start_time = crawl_log.start_time.replace(tzinfo=None) if crawl_log.start_time.tzinfo else crawl_log.start_time
                end_time = crawl_log.end_time.replace(tzinfo=None) if crawl_log.end_time.tzinfo else crawl_log.end_time
                crawl_log.duration = int((end_time - start_time).total_seconds())
            except Exception as duration_error:
                logger.warning(f"计算任务持续时间失败: {duration_error}")
                crawl_log.duration = 0

        crawl_log.error_message = str(e)
        await crawl_log.save()
        await ws_logger.error(f"专业数据爬取失败: {str(e)}")
        raise
