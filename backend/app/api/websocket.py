"""
WebSocket API端点
处理实时通信和任务订阅
"""

import json
import time
from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException
from pydantic import BaseModel
from loguru import logger

from app.websocket import manager
from app.services.task_manager import task_manager, TaskAction


router = APIRouter()


@router.websocket("/crawler")
async def crawler_websocket_endpoint(websocket: WebSocket):
    """爬虫专用WebSocket连接端点"""
    await websocket_endpoint(websocket, f"crawler_{int(time.time() * 1000)}")

@router.websocket("/ws/{connection_id}")
async def websocket_endpoint(websocket: WebSocket, connection_id: str = None):
    """WebSocket连接端点"""
    if not connection_id:
        await websocket.close(code=1008, reason="Missing connection_id")
        return
    
    # 建立连接
    actual_connection_id = await manager.connect(websocket)
    
    try:
        await websocket.send_text(json.dumps({
            "type": "connection",
            "connection_id": actual_connection_id,
            "message": "连接已建立"
        }, ensure_ascii=False))
        
        # 监听客户端消息
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                await handle_websocket_message(actual_connection_id, message)
                
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "无效的JSON格式"
                }, ensure_ascii=False))
                
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: {actual_connection_id}")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
    finally:
        manager.disconnect(actual_connection_id)


async def handle_websocket_message(connection_id: str, message: Dict[str, Any]):
    """处理WebSocket消息"""
    message_type = message.get("type")
    
    if message_type == "subscribe":
        # 订阅任务更新
        task_id = message.get("task_id")
        if task_id:
            manager.subscribe_to_task(connection_id, task_id)
            await manager.send_personal_message({
                "type": "subscribed",
                "task_id": task_id,
                "message": f"已订阅任务 {task_id}"
            }, connection_id)
    
    elif message_type == "unsubscribe":
        # 取消订阅任务更新
        task_id = message.get("task_id")
        if task_id:
            manager.unsubscribe_from_task(connection_id, task_id)
            await manager.send_personal_message({
                "type": "unsubscribed",
                "task_id": task_id,
                "message": f"已取消订阅任务 {task_id}"
            }, connection_id)
    
    elif message_type == "task_action":
        # 任务控制操作
        await handle_task_action(connection_id, message)
    
    elif message_type == "get_status":
        # 获取任务状态
        task_id = message.get("task_id")
        if task_id:
            status = task_manager.get_task_status(task_id)
            await manager.send_personal_message({
                "type": "status",
                "task_id": task_id,
                "status": status
            }, connection_id)
    
    elif message_type == "ping":
        # 心跳检测
        await manager.send_personal_message({
            "type": "pong",
            "timestamp": message.get("timestamp")
        }, connection_id)
    
    else:
        await manager.send_personal_message({
            "type": "error",
            "message": f"未知的消息类型: {message_type}"
        }, connection_id)


async def handle_task_action(connection_id: str, message: Dict[str, Any]):
    """处理任务控制操作"""
    task_id = message.get("task_id")
    action = message.get("action")
    
    if not task_id or not action:
        await manager.send_personal_message({
            "type": "error",
            "message": "缺少task_id或action参数"
        }, connection_id)
        return
    
    try:
        success = False
        result_message = ""
        
        if action == TaskAction.PAUSE:
            success = await task_manager.pause_task(task_id)
            result_message = "任务已暂停" if success else "暂停任务失败"
            
        elif action == TaskAction.RESUME:
            success = await task_manager.resume_task(task_id)
            result_message = "任务已恢复" if success else "恢复任务失败"
            
        elif action == TaskAction.CANCEL:
            success = await task_manager.cancel_task(task_id)
            result_message = "任务已取消" if success else "取消任务失败"
            
        else:
            result_message = f"不支持的操作: {action}"
        
        await manager.send_personal_message({
            "type": "action_result",
            "task_id": task_id,
            "action": action,
            "success": success,
            "message": result_message
        }, connection_id)
        
    except Exception as e:
        logger.error(f"执行任务操作失败: {e}")
        await manager.send_personal_message({
            "type": "error",
            "message": f"执行操作失败: {str(e)}"
        }, connection_id)


@router.get("/connections/stats")
async def get_connection_stats():
    """获取连接统计信息"""
    return {
        "active_connections": manager.get_connection_count(),
        "task_subscriptions": {
            task_id: manager.get_task_subscriber_count(task_id)
            for task_id in manager.task_subscriptions.keys()
        }
    }


@router.get("/tasks/status")
async def get_all_tasks_status():
    """获取所有任务状态"""
    return task_manager.get_all_tasks_status()


@router.get("/tasks/{task_id}/status")
async def get_task_status(task_id: str):
    """获取特定任务状态"""
    status = task_manager.get_task_status(task_id)
    if status.get("status") == "not_found":
        raise HTTPException(status_code=404, detail="任务不存在")
    return status


class TaskActionRequest(BaseModel):
    """任务操作请求"""
    action: str

@router.post("/tasks/{task_id}/action")
async def task_action(task_id: str, request: TaskActionRequest):
    """执行任务操作"""
    try:
        action = request.action
        success = False
        message = ""

        if action == TaskAction.PAUSE:
            success = await task_manager.pause_task(task_id)
            message = "任务已暂停" if success else "暂停任务失败"

        elif action == TaskAction.RESUME:
            success = await task_manager.resume_task(task_id)
            message = "任务已恢复" if success else "恢复任务失败"

        elif action == TaskAction.CANCEL:
            success = await task_manager.cancel_task(task_id)
            message = "任务已取消" if success else "取消任务失败"

        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {action}")

        return {
            "success": success,
            "message": message,
            "task_id": task_id,
            "action": action
        }

    except Exception as e:
        logger.error(f"执行任务操作失败: {e}")
        raise HTTPException(status_code=500, detail=f"执行操作失败: {str(e)}")
