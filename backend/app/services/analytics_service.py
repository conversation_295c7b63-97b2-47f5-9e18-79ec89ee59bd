"""
数据分析服务 - 高考数据统计分析和可视化数据生成
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

# 尝试导入pandas
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    pd = None

# 尝试导入日志库
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

# 尝试导入Tortoise函数
try:
    from tortoise.functions import Count, Avg, Sum, Min, Max
    TORTOISE_FUNCTIONS_AVAILABLE = True
except ImportError:
    TORTOISE_FUNCTIONS_AVAILABLE = False

from app.models import University, Major, AdmissionRecord, MajorAdmissionRecord

# 尝试导入缓存服务
try:
    from app.services.cache_service import cache_service, cache_result, CacheKeys
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False

    # 简化的缓存装饰器
    def cache_result(key_prefix: str = "", ttl: int = 3600, serialize: bool = True):
        def decorator(func):
            async def wrapper(*args, **kwargs):
                return await func(*args, **kwargs)
            return wrapper
        return decorator


class AnalyticsService:
    """数据分析服务"""
    
    @cache_result(key_prefix="analytics", ttl=3600)
    async def get_overview_statistics(self) -> Dict[str, Any]:
        """获取数据概览统计"""
        try:
            # 基础统计
            total_universities = await University.all().count()
            total_majors = await Major.all().count()
            total_admission_records = await AdmissionRecord.all().count()
            total_major_records = await MajorAdmissionRecord.all().count()
            
            # 年份统计
            years = await AdmissionRecord.all().distinct().values_list("year", flat=True)
            year_range = f"{min(years)}-{max(years)}" if years else "无数据"
            
            # 省份统计
            provinces = await AdmissionRecord.all().distinct().values_list("province_name", flat=True)
            province_count = len(set(filter(None, provinces)))
            
            # 985/211统计
            universities_985 = await University.filter(is_985=True).count()
            universities_211 = await University.filter(is_211=True).count()
            
            return {
                "basic_stats": {
                    "total_universities": total_universities,
                    "total_majors": total_majors,
                    "total_admission_records": total_admission_records,
                    "total_major_records": total_major_records,
                    "universities_985": universities_985,
                    "universities_211": universities_211,
                },
                "coverage": {
                    "year_range": year_range,
                    "total_years": len(set(years)) if years else 0,
                    "province_count": province_count,
                },
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取概览统计失败: {e}")
            return {}
    
    @cache_result(key_prefix="analytics", ttl=1800)
    async def get_admission_trends(
        self, 
        year_start: int = 2020, 
        year_end: int = 2024
    ) -> Dict[str, Any]:
        """获取录取趋势分析"""
        try:
            # 查询录取数据
            records = await AdmissionRecord.filter(
                year__gte=year_start,
                year__lte=year_end
            ).values(
                "year", "province_name", "science_code",
                "min_score", "avg_score", "max_score", "min_score_rank"
            )
            
            if not records:
                return {"trends": [], "summary": {}}
            
            # 转换为DataFrame进行分析
            df = pd.DataFrame(records)
            
            # 按年份分组统计
            yearly_stats = df.groupby('year').agg({
                'min_score': ['mean', 'min', 'max'],
                'avg_score': ['mean', 'min', 'max'],
                'max_score': ['mean', 'min', 'max'],
                'min_rank': ['mean', 'min', 'max']
            }).round(2)
            
            # 构建趋势数据
            trends = []
            for year in yearly_stats.index:
                trends.append({
                    "year": int(year),
                    "avg_min_score": float(yearly_stats.loc[year, ('min_score', 'mean')]),
                    "avg_avg_score": float(yearly_stats.loc[year, ('avg_score', 'mean')]),
                    "avg_max_score": float(yearly_stats.loc[year, ('max_score', 'mean')]),
                    "best_rank": int(yearly_stats.loc[year, ('min_rank', 'min')]),
                    "avg_rank": int(yearly_stats.loc[year, ('min_rank', 'mean')])
                })
            
            # 计算变化趋势
            if len(trends) >= 2:
                latest = trends[-1]
                previous = trends[-2]
                score_change = latest["avg_avg_score"] - previous["avg_avg_score"]
                rank_change = latest["avg_rank"] - previous["avg_rank"]
            else:
                score_change = 0
                rank_change = 0
            
            return {
                "trends": trends,
                "summary": {
                    "total_years": len(trends),
                    "score_trend": "上升" if score_change > 0 else "下降" if score_change < 0 else "稳定",
                    "score_change": round(score_change, 2),
                    "rank_change": int(rank_change)
                }
            }
            
        except Exception as e:
            logger.error(f"获取录取趋势失败: {e}")
            return {"trends": [], "summary": {}}
    
    @cache_result(key_prefix="analytics", ttl=1800)
    async def get_university_rankings(
        self, 
        year: int = 2024, 
        science_type: str = "5",
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取大学排名分析"""
        try:
            # 查询录取数据并关联大学信息
            records = await AdmissionRecord.filter(
                year=year,
                science_type=science_type
            ).select_related("university").order_by("min_rank")
            
            rankings = []
            for i, record in enumerate(records[:limit]):
                university = record.university
                rankings.append({
                    "rank": i + 1,
                    "university_code": university.code,
                    "university_name": university.name,
                    "is_985": university.is_985,
                    "is_211": university.is_211,
                    "university_type": university.university_type,
                    "location": university.location,
                    "min_score": record.min_score,
                    "avg_score": record.avg_score,
                    "max_score": record.max_score,
                    "min_rank": record.min_rank,
                    "province": record.province_name
                })
            
            return rankings
            
        except Exception as e:
            logger.error(f"获取大学排名失败: {e}")
            return []
    
    @cache_result(key_prefix="analytics", ttl=1800)
    async def get_major_popularity(
        self, 
        year: int = 2024,
        limit: int = 30
    ) -> List[Dict[str, Any]]:
        """获取专业热度分析"""
        try:
            # 统计专业录取记录数量
            major_stats = await MajorAdmissionRecord.filter(
                year=year
            ).select_related("major").values(
                "major__code", "major__name", "major__category"
            ).annotate(
                record_count=Count("id"),
                avg_min_score=Avg("min_score"),
                avg_max_score=Avg("max_score"),
                total_enrollment=Sum("enrollment_count")
            ).order_by("-record_count")
            
            popularity = []
            for i, stat in enumerate(major_stats[:limit]):
                popularity.append({
                    "rank": i + 1,
                    "major_code": stat["major__code"],
                    "major_name": stat["major__name"],
                    "category": stat["major__category"],
                    "record_count": stat["record_count"],
                    "avg_min_score": round(stat["avg_min_score"] or 0, 2),
                    "avg_max_score": round(stat["avg_max_score"] or 0, 2),
                    "total_enrollment": stat["total_enrollment"] or 0
                })
            
            return popularity
            
        except Exception as e:
            logger.error(f"获取专业热度失败: {e}")
            return []
    
    @cache_result(key_prefix="analytics", ttl=1800)
    async def get_province_competition(
        self, 
        year: int = 2024,
        science_type: str = "5"
    ) -> List[Dict[str, Any]]:
        """获取省份竞争分析"""
        try:
            # 按省份统计录取数据
            province_stats = await AdmissionRecord.filter(
                year=year,
                science_code=science_type
            ).values("province_name").annotate(
                university_count=Count("university", distinct=True),
                avg_min_score=Avg("min_score"),
                avg_max_score=Avg("max_score"),
                best_rank=Min("min_score_rank"),
                worst_rank=Max("min_score_rank")
            ).order_by("-avg_min_score")
            
            competition = []
            for i, stat in enumerate(province_stats):
                # 计算竞争激烈程度
                score_range = (stat["avg_max_score"] or 0) - (stat["avg_min_score"] or 0)
                rank_range = (stat["worst_rank"] or 0) - (stat["best_rank"] or 0)
                
                competition.append({
                    "rank": i + 1,
                    "province": stat["province_name"],
                    "university_count": stat["university_count"],
                    "avg_min_score": round(stat["avg_min_score"] or 0, 2),
                    "avg_max_score": round(stat["avg_max_score"] or 0, 2),
                    "score_range": round(score_range, 2),
                    "best_rank": stat["best_rank"] or 0,
                    "worst_rank": stat["worst_rank"] or 0,
                    "rank_range": rank_range,
                    "competition_level": self._calculate_competition_level(score_range, rank_range)
                })
            
            return competition
            
        except Exception as e:
            logger.error(f"获取省份竞争分析失败: {e}")
            return []
    
    def _calculate_competition_level(self, score_range: float, rank_range: int) -> str:
        """计算竞争激烈程度"""
        if score_range > 100 or rank_range > 50000:
            return "激烈"
        elif score_range > 50 or rank_range > 20000:
            return "中等"
        else:
            return "较低"
    
    @cache_result(key_prefix="analytics", ttl=3600)
    async def get_score_distribution(
        self, 
        year: int = 2024,
        province: str = "河北",
        science_type: str = "5"
    ) -> Dict[str, Any]:
        """获取分数分布分析"""
        try:
            records = await AdmissionRecord.filter(
                year=year,
                province_name=province,
                science_code=science_type
            ).values_list("min_score", "avg_score", "max_score", flat=False)
            
            if not records:
                return {"distribution": [], "statistics": {}}
            
            # 转换为DataFrame
            df = pd.DataFrame(records, columns=["min_score", "avg_score", "max_score"])
            
            # 计算分数区间分布
            bins = [0, 400, 450, 500, 550, 600, 650, 700, 750]
            labels = ["400以下", "400-450", "450-500", "500-550", "550-600", "600-650", "650-700", "700以上"]
            
            distribution = []
            for score_type in ["min_score", "avg_score", "max_score"]:
                counts = pd.cut(df[score_type], bins=bins, labels=labels, include_lowest=True).value_counts()
                distribution.append({
                    "type": score_type,
                    "data": [{"range": label, "count": int(count)} for label, count in counts.items()]
                })
            
            # 统计信息
            statistics = {
                "total_records": len(df),
                "min_score_stats": {
                    "mean": round(df["min_score"].mean(), 2),
                    "median": round(df["min_score"].median(), 2),
                    "std": round(df["min_score"].std(), 2)
                },
                "avg_score_stats": {
                    "mean": round(df["avg_score"].mean(), 2),
                    "median": round(df["avg_score"].median(), 2),
                    "std": round(df["avg_score"].std(), 2)
                }
            }
            
            return {
                "distribution": distribution,
                "statistics": statistics
            }
            
        except Exception as e:
            logger.error(f"获取分数分布失败: {e}")
            return {"distribution": [], "statistics": {}}


# 全局分析服务实例
analytics_service = AnalyticsService()


# 导出
__all__ = ["AnalyticsService", "analytics_service"]
