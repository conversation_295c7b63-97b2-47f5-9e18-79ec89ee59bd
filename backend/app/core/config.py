"""
统一配置管理 - 环境变量和应用配置
"""

from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List, Optional
import os
from pathlib import Path


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    url: str = Field(default="sqlite:///./gaokao.db", description="数据库连接URL")
    echo: bool = Field(default=False, description="是否打印SQL语句")
    pool_size: int = Field(default=10, description="连接池大小")
    max_overflow: int = Field(default=20, description="连接池最大溢出")

    class Config:
        env_prefix = "DB_"
        extra = "ignore"  # 忽略额外字段


class RedisSettings(BaseSettings):
    """Redis配置"""
    host: str = Field(default="localhost", description="Redis主机")
    port: int = Field(default=6379, description="Redis端口")
    password: Optional[str] = Field(default=None, description="Redis密码")
    db: int = Field(default=0, description="Redis数据库")

    class Config:
        env_prefix = "REDIS_"
        extra = "ignore"


class CrawlerSettings(BaseSettings):
    """爬虫配置"""
    api_token: str = Field(default="your_api_token_here", description="API访问令牌")
    base_url: str = Field(
        default="https://applet.cqzk.com.cn/prod/history/front/history/ptwlList",
        description="API基础URL"
    )
    request_delay: float = Field(default=1.0, description="请求间隔(秒)")
    max_retries: int = Field(default=3, description="最大重试次数")
    timeout: int = Field(default=30, description="请求超时时间(秒)")
    concurrent_limit: int = Field(default=5, description="并发请求限制")
    proxy_enabled: bool = Field(default=False, description="是否启用代理")
    schedule_enabled: bool = Field(default=False, description="是否启用定时任务")

    # API端点配置
    university_endpoint: str = Field(
        default="https://applet.cqzk.com.cn/prod/history/front/history/ptwlList",
        description="大学数据API端点"
    )
    major_endpoint: str = Field(
        default="https://applet.cqzk.com.cn/prod/history/front/history/ptwlMajorList",
        description="专业数据API端点"
    )

    # 请求头配置
    user_agent: str = Field(
        default="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        description="User-Agent"
    )
    accept: str = Field(
        default="application/json, text/plain, */*",
        description="Accept头"
    )

    # 目标年份
    target_years: List[int] = Field(
        default=[2022, 2023, 2024],
        description="目标年份列表"
    )

    # 省份列表
    provinces: List[str] = Field(
        default=[
            "北京", "天津", "河北", "山西", "内蒙古", "辽宁", "吉林", "黑龙江",
            "上海", "江苏", "浙江", "安徽", "福建", "江西", "山东", "河南",
            "湖北", "湖南", "广东", "广西", "海南", "重庆", "四川", "贵州",
            "云南", "西藏", "陕西", "甘肃", "青海", "宁夏", "新疆"
        ],
        description="省份列表"
    )

    # 科类类型映射
    science_types: dict = Field(
        default={
            "1": "历史",
            "5": "物理"
        },
        description="科类类型映射"
    )

    # 批次类型映射
    batch_types: dict = Field(
        default={
            "0": "本科提前批A段",
            "1": "本科提前批B段",
            "2": "本科批",
            "5": "高职专科批"
        },
        description="批次类型映射"
    )

    class Config:
        env_prefix = "CRAWLER_"
        extra = "ignore"


class SecuritySettings(BaseSettings):
    """安全配置"""
    secret_key: str = Field(default="your-secret-key-here", description="JWT密钥")
    algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间")
    allowed_hosts: List[str] = Field(default=["*"], description="允许的主机")

    class Config:
        env_prefix = "SECURITY_"
        extra = "ignore"


class LoggingSettings(BaseSettings):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        description="日志格式"
    )
    rotation: str = Field(default="1 day", description="日志轮转")
    retention: str = Field(default="30 days", description="日志保留时间")

    class Config:
        env_prefix = "LOG_"
        extra = "ignore"


class AppSettings(BaseSettings):
    """应用主配置"""
    title: str = Field(default="高考数据收集分析平台", description="应用标题")
    description: str = Field(
        default="基于API文档设计的高考院校专业数据收集、存储、分析和可视化展示平台",
        description="应用描述"
    )
    version: str = Field(default="0.2.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    host: str = Field(default="0.0.0.0", description="服务主机")
    port: int = Field(default=8000, description="服务端口")

    # 子配置
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    crawler: CrawlerSettings = Field(default_factory=CrawlerSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"  # 忽略额外的环境变量


# 全局配置实例
settings = AppSettings()


def get_settings() -> AppSettings:
    """获取配置实例"""
    return settings


# 环境配置检查
def validate_environment():
    """验证环境配置"""
    required_vars = [
        "CRAWLER_API_TOKEN",
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise ValueError(f"缺少必需的环境变量: {', '.join(missing_vars)}")
    
    return True


# 配置初始化
def init_config():
    """初始化配置"""
    # 创建必要的目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # 验证环境
    try:
        validate_environment()
        print("✅ 环境配置验证通过")
    except ValueError as e:
        print(f"⚠️ 环境配置警告: {e}")
    
    return settings
