#!/usr/bin/env python3
"""
测试任务创建功能
"""

import asyncio
import aiohttp
import json
from datetime import datetime

API_BASE = "http://localhost:8000"

async def test_task_creation():
    """测试任务创建功能"""
    print("🚀 开始测试任务创建功能...")
    
    async with aiohttp.ClientSession() as session:
        # 1. 测试健康检查
        print("\n1. 测试健康检查...")
        try:
            async with session.get(f"{API_BASE}/api/health") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 健康检查通过: {data['status']}")
                else:
                    print(f"❌ 健康检查失败: {resp.status}")
                    return
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return
        
        # 2. 测试爬虫配置
        print("\n2. 测试爬虫配置...")
        try:
            async with session.get(f"{API_BASE}/api/crawler/config") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 爬虫配置获取成功")
                    print(f"   - 目标年份: {data['data']['target_years']}")
                    print(f"   - 省份数量: {len(data['data']['provinces'])}")
                    print(f"   - 科类: {data['data']['science_types']}")
                else:
                    print(f"❌ 爬虫配置获取失败: {resp.status}")
        except Exception as e:
            print(f"❌ 爬虫配置异常: {e}")
        
        # 3. 测试任务列表
        print("\n3. 测试任务列表...")
        try:
            async with session.get(f"{API_BASE}/api/crawler/tasks") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 任务列表获取成功")
                    print(f"   - 总任务数: {data['total']}")
                    print(f"   - 当前页任务: {len(data['data'])}")
                    
                    # 显示最近的几个任务
                    for task in data['data'][:3]:
                        print(f"   - {task['task_name']}: {task['status']} ({task['task_id']})")
                else:
                    print(f"❌ 任务列表获取失败: {resp.status}")
        except Exception as e:
            print(f"❌ 任务列表异常: {e}")
        
        # 4. 测试创建小规模任务
        print("\n4. 测试创建小规模任务...")
        task_data = {
            "years": [2024],
            "provinces": ["北京"],
            "science_types": ["1"],  # 历史类
            "batch_types": ["2"]     # 本科批
        }
        
        try:
            headers = {"Content-Type": "application/json"}
            async with session.post(
                f"{API_BASE}/api/crawler/universities/start",
                json=task_data,
                headers=headers
            ) as resp:
                response_text = await resp.text()
                print(f"响应状态: {resp.status}")
                print(f"响应内容: {response_text}")
                
                if resp.status == 200:
                    data = await resp.json() if response_text else {}
                    print(f"✅ 任务创建成功!")
                    print(f"   - 任务ID: {data.get('task_id', 'N/A')}")
                    print(f"   - 消息: {data.get('message', 'N/A')}")
                    print(f"   - 状态: {data.get('status', 'N/A')}")
                    
                    # 等待一下，然后检查任务状态
                    task_id = data.get('task_id')
                    if task_id:
                        await asyncio.sleep(2)
                        await check_task_status(session, task_id)
                        
                else:
                    print(f"❌ 任务创建失败: {resp.status}")
                    print(f"   响应: {response_text}")
                    
        except Exception as e:
            print(f"❌ 任务创建异常: {e}")
        
        # 5. 测试WebSocket连接
        print("\n5. 测试WebSocket连接...")
        try:
            import websockets
            
            async with websockets.connect(f"ws://localhost:8000/api/ws/crawler") as websocket:
                print("✅ WebSocket连接成功")
                
                # 等待接收消息
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    print(f"✅ 收到WebSocket消息: {data.get('type', 'unknown')}")
                except asyncio.TimeoutError:
                    print("⚠️ WebSocket连接正常，但5秒内未收到消息")
                    
        except ImportError:
            print("⚠️ websockets库未安装，跳过WebSocket测试")
        except Exception as e:
            print(f"❌ WebSocket连接异常: {e}")

async def check_task_status(session, task_id):
    """检查任务状态"""
    print(f"\n📊 检查任务状态: {task_id}")
    try:
        async with session.get(f"{API_BASE}/api/crawler/tasks/{task_id}") as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✅ 任务状态查询成功")
                print(f"   - 状态: {data.get('status', 'N/A')}")
                print(f"   - 开始时间: {data.get('start_time', 'N/A')}")
                print(f"   - 总请求数: {data.get('total_requests', 0)}")
                print(f"   - 成功请求: {data.get('success_requests', 0)}")
                print(f"   - 总记录数: {data.get('total_records', 0)}")
            else:
                response_text = await resp.text()
                print(f"❌ 任务状态查询失败: {resp.status}")
                print(f"   响应: {response_text}")
    except Exception as e:
        print(f"❌ 任务状态查询异常: {e}")

async def test_analytics_apis():
    """测试数据分析API"""
    print("\n📈 测试数据分析API...")
    
    async with aiohttp.ClientSession() as session:
        # 测试概览统计
        try:
            async with session.get(f"{API_BASE}/api/analytics/overview") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 数据概览获取成功")
                    if data.get('success'):
                        stats = data['data']['basic_stats']
                        print(f"   - 大学数量: {stats['total_universities']}")
                        print(f"   - 专业数量: {stats['total_majors']}")
                        print(f"   - 录取记录: {stats['total_admission_records']}")
                    else:
                        print(f"   - 错误: {data.get('error', {}).get('message', 'Unknown')}")
                else:
                    print(f"❌ 数据概览获取失败: {resp.status}")
        except Exception as e:
            print(f"❌ 数据概览异常: {e}")
        
        # 测试趋势分析
        try:
            async with session.get(f"{API_BASE}/api/analytics/trends") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 趋势分析获取成功")
                    if data.get('success'):
                        trends = data['data']['trends']
                        print(f"   - 趋势数据点: {len(trends)}")
                    else:
                        print(f"   - 错误: {data.get('error', {}).get('message', 'Unknown')}")
                else:
                    print(f"❌ 趋势分析获取失败: {resp.status}")
        except Exception as e:
            print(f"❌ 趋势分析异常: {e}")

async def main():
    """主函数"""
    print("=" * 60)
    print("🎯 高考数据平台功能验证测试")
    print("=" * 60)
    
    # 基础功能测试
    await test_task_creation()
    
    # 数据分析测试
    await test_analytics_apis()
    
    print("\n" + "=" * 60)
    print("✅ 功能验证测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
